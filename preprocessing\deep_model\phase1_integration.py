"""Phase 1 Integration Module for Deep Learning Models.

This module contains the main Phase 1 integration functions that combine
optimized preprocessing with the original deep learning pipeline.

Key Functions:
- impute_logs_deep_phase1: Enhanced version with Phase 1 preprocessing
- impute_logs_deep_phase1_optimized: Optimized version with adaptive settings
- impute_logs_deep_phase1_safe: Safe wrapper with hardware optimization
- validate_training_sequences: Training-aware validation functions
"""

import numpy as np
import pandas as pd
import torch
import time
import warnings
import logging
from typing import Dict, List, Tuple, Any, Optional, Union

# Import Phase 1 preprocessing functions
try:
    from .phase1_preprocessing import (
        OptimizationConfig,
        OPTIMIZATION_CONFIGS,
        SmartValidator,
        vectorized_preprocessing_pipeline,
        gpu_accelerated_preprocessing,
        quick_data_quality_check,
        impute_logs_deep_tensor_native,
        fast_path_tensor_training,
        configure_optimization_for_hardware
    )
except ImportError:
    warnings.warn("Phase 1 preprocessing functions not available. Some functionality may be limited.")

# Try to import Phase 1 core functions
try:
    from .stability_preprocessing import (
        phase1_preprocessing_pipeline,
        enhanced_validate_sequences,
        get_recommended_preprocessing_config,
        encode_missing_values
    )
    PHASE1_AVAILABLE = True
except ImportError:
    PHASE1_AVAILABLE = False
    warnings.warn("Phase 1 core functions not available. Some functions will use fallback implementations.")

# Try to import original functions for fallback
try:
    from core_code.ml_core import (
        impute_logs_deep as original_impute_logs_deep,
        normalize_data,
        create_sequences,
        introduce_missingness
    )
except ImportError:
    warnings.warn("Original ml_core functions not available. Some fallback functionality may be limited.")


def impute_logs_deep_phase1_optimized(df: pd.DataFrame,
                                     target_col: str,
                                     feature_cols: List[str],
                                     sequence_length: int = 32,
                                     model_config: Optional[Dict[str, Any]] = None,
                                     hparams: Optional[Dict[str, Any]] = None,
                                     optimization_level: str = "moderate",
                                     enable_diagnostics: bool = True) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Optimized version of impute_logs_deep with Phase 1 preprocessing integration.
    
    This function provides adaptive optimization based on dataset characteristics
    and hardware capabilities.
    
    Args:
        df: Input DataFrame with missing values
        target_col: Target column name
        feature_cols: List of feature column names
        sequence_length: Length of sequences for model training
        model_config: Model configuration dictionary
        hparams: Hyperparameters dictionary
        optimization_level: Optimization level ("conservative", "moderate", "aggressive")
        enable_diagnostics: Whether to enable diagnostic reporting
        
    Returns:
        Tuple of (imputed_dataframe, metadata_dict)
    """
    print(f"🚀 PHASE 1 OPTIMIZED IMPUTATION")
    print(f"   Dataset: {df.shape}, Target: {target_col}")
    print(f"   Optimization: {optimization_level}")
    
    start_time = time.time()
    
    # Get optimization configuration
    if optimization_level in OPTIMIZATION_CONFIGS:
        config = OPTIMIZATION_CONFIGS[optimization_level]
    else:
        config = configure_optimization_for_hardware()
    
    # Adaptive optimization based on dataset size
    dataset_size = np.prod(df.shape)
    if dataset_size < 10000:
        print("   📊 Small dataset detected - using conservative optimization")
        config = OPTIMIZATION_CONFIGS["conservative"]
    elif dataset_size > 100000:
        print("   📊 Large dataset detected - enabling aggressive optimization")
        config = OPTIMIZATION_CONFIGS["aggressive"]
    
    # Adjust sequence length based on dataset characteristics
    if len(df) < sequence_length * 3:
        adjusted_sequence_length = max(8, len(df) // 3)
        print(f"   ⚠️ Adjusting sequence length: {sequence_length} → {adjusted_sequence_length}")
        sequence_length = adjusted_sequence_length
    
    try:
        # Quick data quality assessment
        if config.enable_fast_path:
            quality_score = quick_data_quality_check(df[feature_cols].values)
            print(f"   📈 Data quality score: {quality_score:.3f}")
            
            if quality_score >= config.early_exit_quality_threshold:
                print(f"   ⚡ High quality data - using fast path")
                return _fast_path_imputation(df, target_col, feature_cols, sequence_length, config)
        
        # Use vectorized preprocessing if enabled
        if config.use_vectorized_preprocessing:
            print(f"   🔄 Using vectorized preprocessing pipeline")
            return _vectorized_imputation(df, target_col, feature_cols, sequence_length, config)
        
        # Smart validation
        if config.enable_smart_validation:
            validator = SmartValidator(config)
            sequences = create_sequences(df[feature_cols].values, sequence_length)
            if not validator.validate_sequences(sequences, feature_cols):
                print(f"   ⚠️ Validation failed - falling back to conservative processing")
                config = OPTIMIZATION_CONFIGS["conservative"]
        
        # Standard Phase 1 processing
        return impute_logs_deep_phase1(
            df, target_col, feature_cols, sequence_length, model_config, hparams
        )
        
    except Exception as e:
        print(f"   ❌ Optimization failed: {e}")
        print(f"   🔄 Falling back to original implementation")
        
        # Fallback to original implementation
        if 'original_impute_logs_deep' in globals():
            result_df = original_impute_logs_deep(df, target_col, feature_cols, sequence_length)
            metadata = {
                'processing_time': time.time() - start_time,
                'optimization_used': False,
                'fallback_reason': str(e)
            }
            return result_df, metadata
        else:
            raise RuntimeError(f"Both optimized and original implementations failed: {e}")


def _fast_path_imputation(df: pd.DataFrame,
                         target_col: str,
                         feature_cols: List[str],
                         sequence_length: int,
                         config: OptimizationConfig) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Fast path imputation for high-quality data.
    
    Args:
        df: Input DataFrame
        target_col: Target column name
        feature_cols: List of feature column names
        sequence_length: Sequence length
        config: Optimization configuration
        
    Returns:
        Tuple of (imputed_dataframe, metadata_dict)
    """
    print(f"   ⚡ Fast path imputation")
    
    start_time = time.time()
    
    # Simple forward fill for high-quality data
    result_df = df.copy()
    for col in feature_cols:
        result_df[col] = result_df[col].fillna(method='ffill').fillna(method='bfill')
    
    # Fill any remaining NaN values with column means
    for col in feature_cols:
        if result_df[col].isna().any():
            result_df[col] = result_df[col].fillna(result_df[col].mean())
    
    metadata = {
        'processing_time': time.time() - start_time,
        'method': 'fast_path',
        'optimization_config': config.__dict__
    }
    
    return result_df, metadata


def _vectorized_imputation(df: pd.DataFrame,
                          target_col: str,
                          feature_cols: List[str],
                          sequence_length: int,
                          config: OptimizationConfig) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Vectorized imputation using optimized preprocessing.
    
    Args:
        df: Input DataFrame
        target_col: Target column name
        feature_cols: List of feature column names
        sequence_length: Sequence length
        config: Optimization configuration
        
    Returns:
        Tuple of (imputed_dataframe, metadata_dict)
    """
    print(f"   🔄 Vectorized imputation")
    
    start_time = time.time()
    
    # Create sequences
    sequences = create_sequences(df[feature_cols].values, sequence_length)
    
    # Apply vectorized preprocessing
    processed_sequences = vectorized_preprocessing_pipeline(sequences, feature_cols, config)
    
    # Simple imputation using processed sequences
    # Flatten sequences back to original format
    n_sequences, seq_len, n_features = processed_sequences.shape
    flattened = processed_sequences.reshape(-1, n_features)
    
    # Create result DataFrame
    result_df = df.copy()
    
    # Fill missing values using processed data
    for i, col in enumerate(feature_cols):
        if i < n_features:
            # Use mean of processed sequences for imputation
            finite_values = flattened[:, i][np.isfinite(flattened[:, i])]
            if len(finite_values) > 0:
                fill_value = np.mean(finite_values)
                result_df[col] = result_df[col].fillna(fill_value)
    
    metadata = {
        'processing_time': time.time() - start_time,
        'method': 'vectorized',
        'sequences_shape': processed_sequences.shape,
        'optimization_config': config.__dict__
    }
    
    return result_df, metadata


def impute_logs_deep_phase1_safe(df: pd.DataFrame,
                                target_col: str,
                                feature_cols: List[str],
                                sequence_length: int = 32,
                                model_config: Optional[Dict[str, Any]] = None,
                                hparams: Optional[Dict[str, Any]] = None,
                                enable_gpu_optimization: bool = True) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Safe wrapper for Phase 1 imputation with hardware-specific optimizations.
    
    This function includes GPU hardware detection and applies appropriate
    optimizations based on the detected hardware capabilities.
    
    Args:
        df: Input DataFrame with missing values
        target_col: Target column name
        feature_cols: List of feature column names
        sequence_length: Length of sequences for model training
        model_config: Model configuration dictionary
        hparams: Hyperparameters dictionary
        enable_gpu_optimization: Whether to enable GPU-specific optimizations
        
    Returns:
        Tuple of (imputed_dataframe, metadata_dict)
    """
    print(f"🛡️ PHASE 1 SAFE IMPUTATION")
    print(f"   Dataset: {df.shape}, Target: {target_col}")
    
    start_time = time.time()
    metadata = {
        'start_time': start_time,
        'gpu_available': torch.cuda.is_available(),
        'gpu_optimization_enabled': enable_gpu_optimization
    }
    
    try:
        # GPU hardware detection and optimization
        if torch.cuda.is_available() and enable_gpu_optimization:
            gpu_name = torch.cuda.get_device_name()
            capability = torch.cuda.get_device_capability()
            
            print(f"   🎯 GPU detected: {gpu_name} (Compute {capability[0]}.{capability[1]})")
            metadata['gpu_name'] = gpu_name
            metadata['gpu_capability'] = capability
            
            # Hardware-specific optimizations
            if capability[0] <= 6:  # Pascal and older
                print(f"   ⚠️ Pascal GPU detected - disabling mixed precision")
                if hparams is None:
                    hparams = {}
                hparams['use_mixed_precision'] = False
                
                # Optimize batch size for Pascal
                if model_config is None:
                    model_config = {}
                model_config['batch_size'] = min(model_config.get('batch_size', 32), 16)
                
            elif capability[0] >= 7:  # Volta and newer
                print(f"   🚀 Modern GPU detected - enabling mixed precision")
                if hparams is None:
                    hparams = {}
                hparams['use_mixed_precision'] = True
        
        # Attempt optimized pipeline
        try:
            print(f"   🔄 Attempting optimized pipeline")
            result_df, opt_metadata = impute_logs_deep_phase1_optimized(
                df, target_col, feature_cols, sequence_length, 
                model_config, hparams, "moderate", True
            )
            
            metadata.update(opt_metadata)
            metadata['optimization_successful'] = True
            
            return result_df, metadata
            
        except Exception as opt_error:
            print(f"   ⚠️ Optimized pipeline failed: {opt_error}")
            metadata['optimization_error'] = str(opt_error)
            metadata['optimization_successful'] = False
        
        # Fallback to standard Phase 1 implementation
        print(f"   🔄 Falling back to standard Phase 1 implementation")
        result_df, phase1_metadata = impute_logs_deep_phase1(
            df, target_col, feature_cols, sequence_length, model_config, hparams
        )
        
        metadata.update(phase1_metadata)
        metadata['fallback_used'] = 'phase1_standard'
        
        return result_df, metadata
        
    except Exception as e:
        print(f"   ❌ Phase 1 implementation failed: {e}")
        metadata['phase1_error'] = str(e)
        
        # Final fallback to original implementation
        if 'original_impute_logs_deep' in globals():
            print(f"   🔄 Final fallback to original implementation")
            result_df = original_impute_logs_deep(df, target_col, feature_cols, sequence_length)
            
            metadata['fallback_used'] = 'original'
            metadata['processing_time'] = time.time() - start_time
            
            return result_df, metadata
        else:
            raise RuntimeError(f"All imputation methods failed: {e}")


def impute_logs_deep_phase1(df: pd.DataFrame,
                           target_col: str,
                           feature_cols: List[str],
                           sequence_length: int = 32,
                           model_config: Optional[Dict[str, Any]] = None,
                           hparams: Optional[Dict[str, Any]] = None) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Enhanced version of impute_logs_deep with Phase 1 preprocessing integration.
    
    This function integrates Phase 1 preprocessing pipeline with the original
    deep learning imputation process.
    
    Args:
        df: Input DataFrame with missing values
        target_col: Target column name
        feature_cols: List of feature column names
        sequence_length: Length of sequences for model training
        model_config: Model configuration dictionary
        hparams: Hyperparameters dictionary
        
    Returns:
        Tuple of (imputed_dataframe, metadata_dict)
    """
    print(f"🔬 PHASE 1 INTEGRATION")
    print(f"   Dataset: {df.shape}, Features: {len(feature_cols)}")
    print(f"   Target: {target_col}, Sequence length: {sequence_length}")
    
    start_time = time.time()
    
    try:
        # Phase 1: Initial data preparation
        print(f"   📊 Phase 1: Data preparation")
        
        # Normalize data
        normalized_data = normalize_data(df[feature_cols])
        
        # Create sequences
        sequences = create_sequences(normalized_data.values, sequence_length)
        print(f"   📦 Created sequences: {sequences.shape}")
        
        # Apply Phase 1 preprocessing pipeline if available
        if PHASE1_AVAILABLE:
            print(f"   🔬 Applying Phase 1 preprocessing pipeline")
            
            # Get recommended preprocessing configuration
            preprocessing_config = get_recommended_preprocessing_config(
                sequences, feature_cols, target_col
            )
            
            # Apply Phase 1 preprocessing
            preprocessing_result = phase1_preprocessing_pipeline(
                sequences, feature_cols, preprocessing_config
            )
            
            # Handle different return value formats
            if isinstance(preprocessing_result, tuple):
                processed_sequences, preprocessing_metadata = preprocessing_result
            else:
                processed_sequences = preprocessing_result
                preprocessing_metadata = {}
            
            print(f"   ✅ Phase 1 preprocessing completed")
        else:
            print(f"   ⚠️ Phase 1 preprocessing not available - using standard preprocessing")
            processed_sequences = sequences
            preprocessing_metadata = {'phase1_available': False}
        
        # Phase 2: Prepare training sequences
        print(f"   🎯 Phase 2: Training preparation")
        
        # Introduce missingness for imputation training
        if model_config and model_config.get('training_mode', True):
            train_sequences = introduce_missingness(processed_sequences, missing_rate=0.1)
            truth_sequences = processed_sequences.copy()
        else:
            # Prediction-only mode - use sequences directly
            train_sequences = processed_sequences
            truth_sequences = processed_sequences
        
        # Encode artificial missing values
        if PHASE1_AVAILABLE:
            train_sequences = encode_missing_values(train_sequences)
        
        # Enhanced pre-training validation
        print(f"   🔍 Enhanced validation")
        
        if PHASE1_AVAILABLE:
            train_valid = enhanced_validate_sequences(train_sequences, feature_cols)
            truth_valid = enhanced_validate_sequences(truth_sequences, feature_cols)
        else:
            train_valid = validate_training_sequences(train_sequences, feature_cols)
            truth_valid = validate_training_sequences(truth_sequences, feature_cols)
        
        if not train_valid or not truth_valid:
            raise ValueError("Sequence validation failed")
        
        print(f"   ✅ Validation passed")
        
        # Phase 3: Model training and imputation
        print(f"   🤖 Phase 3: Model training")
        
        # Convert to tensors for model training
        train_tensor = torch.from_numpy(train_sequences).float()
        truth_tensor = torch.from_numpy(truth_sequences).float()
        
        # Call original imputation with processed sequences
        # Flatten sequences for compatibility with original function
        flattened_sequences = train_sequences.reshape(-1, train_sequences.shape[-1])
        
        # Create temporary DataFrame for original function
        temp_df = pd.DataFrame(flattened_sequences, columns=feature_cols)
        
        # Call original imputation function
        if 'original_impute_logs_deep' in globals():
            result_df = original_impute_logs_deep(temp_df, target_col, feature_cols, sequence_length)
        else:
            # Fallback: simple imputation
            result_df = df.copy()
            for col in feature_cols:
                result_df[col] = result_df[col].fillna(result_df[col].mean())
        
        # Add Phase 1 metadata
        metadata = {
            'processing_time': time.time() - start_time,
            'phase1_available': PHASE1_AVAILABLE,
            'preprocessing_metadata': preprocessing_metadata,
            'sequences_shape': sequences.shape,
            'processed_sequences_shape': processed_sequences.shape,
            'validation_passed': True
        }
        
        print(f"   ✅ Phase 1 integration completed in {metadata['processing_time']:.3f}s")
        
        return result_df, metadata
        
    except Exception as e:
        print(f"   ❌ Phase 1 integration failed: {e}")
        
        # Fallback to original implementation
        if 'original_impute_logs_deep' in globals():
            print(f"   🔄 Falling back to original implementation")
            result_df = original_impute_logs_deep(df, target_col, feature_cols, sequence_length)
            
            metadata = {
                'processing_time': time.time() - start_time,
                'phase1_available': False,
                'fallback_used': True,
                'error': str(e)
            }
            
            return result_df, metadata
        else:
            raise RuntimeError(f"Both Phase 1 and original implementations failed: {e}")


def validate_training_sequences(sequences: np.ndarray,
                              feature_names: List[str],
                              allow_missing: bool = True) -> bool:
    """
    Training-aware validation that allows intentional missing values.
    
    Args:
        sequences: Input sequences array
        feature_names: List of feature names
        allow_missing: Whether to allow missing values in training sequences
        
    Returns:
        True if sequences are valid for training
    """
    try:
        if sequences.size == 0:
            return False
        
        # Check for infinite values (always invalid)
        if np.any(np.isinf(sequences)):
            return False
        
        # Check for extreme values
        finite_data = sequences[np.isfinite(sequences)]
        if len(finite_data) == 0:
            return not allow_missing  # If no finite data, only valid if missing not allowed
        
        max_abs = np.max(np.abs(finite_data))
        if max_abs > 1e6:
            return False
        
        # Check missing value ratio
        if allow_missing:
            missing_ratio = np.sum(np.isnan(sequences)) / sequences.size
            if missing_ratio > 0.8:  # Too many missing values
                return False
        else:
            if np.any(np.isnan(sequences)):
                return False
        
        return True
        
    except Exception:
        return False


def validate_training_sequences_with_missing(sequences: np.ndarray,
                                           feature_names: List[str],
                                           missing_threshold: float = 0.5) -> bool:
    """
    Validate sequences with controlled missing values for training stability.
    
    Args:
        sequences: Input sequences array
        feature_names: List of feature names
        missing_threshold: Maximum allowed missing value ratio
        
    Returns:
        True if sequences are suitable for training
    """
    try:
        # Basic shape validation
        if len(sequences.shape) != 3:
            return False
        
        n_sequences, seq_len, n_features = sequences.shape
        
        if n_sequences == 0 or seq_len == 0 or n_features == 0:
            return False
        
        # Check missing value ratio
        total_values = np.prod(sequences.shape)
        missing_count = np.sum(np.isnan(sequences))
        missing_ratio = missing_count / total_values
        
        if missing_ratio > missing_threshold:
            return False
        
        # Check for infinite values
        if np.any(np.isinf(sequences)):
            return False
        
        # Check for extreme values in finite data
        finite_mask = np.isfinite(sequences)
        if np.sum(finite_mask) == 0:
            return False
        
        finite_data = sequences[finite_mask]
        max_abs = np.max(np.abs(finite_data))
        
        if max_abs > 1e6:
            return False
        
        # Feature-wise validation
        for feat_idx in range(n_features):
            feature_data = sequences[:, :, feat_idx]
            feature_finite = feature_data[np.isfinite(feature_data)]
            
            if len(feature_finite) == 0:
                return False
            
            # Check for reasonable feature distribution
            feature_std = np.std(feature_finite)
            if feature_std == 0:  # Constant feature
                continue
            
            # Check for reasonable range
            feature_range = np.max(feature_finite) - np.min(feature_finite)
            if feature_range > 1e6:
                return False
        
        return True
        
    except Exception:
        return False


def validate_batch_before_training(batch_data: Union[np.ndarray, torch.Tensor],
                                 feature_names: List[str],
                                 check_gradients: bool = True) -> bool:
    """
    Validate a batch before training to prevent non-finite gradient issues.
    
    Args:
        batch_data: Batch data (numpy array or torch tensor)
        feature_names: List of feature names
        check_gradients: Whether to perform gradient-related checks
        
    Returns:
        True if batch is safe for training
    """
    try:
        # Convert to numpy if torch tensor
        if isinstance(batch_data, torch.Tensor):
            data = batch_data.detach().cpu().numpy()
        else:
            data = batch_data
        
        # Basic validation
        if data.size == 0:
            return False
        
        # Check for NaN values
        if np.any(np.isnan(data)):
            return False
        
        # Check for infinite values
        if np.any(np.isinf(data)):
            return False
        
        # Check for extreme values that could cause gradient issues
        max_abs = np.max(np.abs(data))
        if max_abs > 1e4:  # Conservative threshold for training stability
            return False
        
        # Gradient-specific checks
        if check_gradients:
            # Check for values that could cause gradient explosion
            if max_abs > 100:
                return False
            
            # Check for very small values that could cause vanishing gradients
            min_abs = np.min(np.abs(data[data != 0]))
            if min_abs < 1e-6:
                return False
        
        return True
        
    except Exception:
        return False


# Export all integration functions
__all__ = [
    # Main Phase 1 integration functions
    'impute_logs_deep_phase1',
    'impute_logs_deep_phase1_optimized',
    'impute_logs_deep_phase1_safe',
    
    # Validation functions
    'validate_training_sequences',
    'validate_training_sequences_with_missing',
    'validate_batch_before_training',
    
    # Helper functions
    '_fast_path_imputation',
    '_vectorized_imputation'
]