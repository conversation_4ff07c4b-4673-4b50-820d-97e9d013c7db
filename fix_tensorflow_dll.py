#!/usr/bin/env python3
"""
TensorFlow DLL Fix Script

This script provides an automated solution to fix TensorFlow DLL loading issues
on Windows systems. It handles package uninstallation, reinstallation with
compatible versions, and environment configuration.

Usage:
    python fix_tensorflow_dll.py

Author: ML Log Prediction System
Date: 2025-08-19
"""

import subprocess
import sys
import os
import warnings
from typing import List, Tuple, Optional

def run_command(command: List[str], description: str) -> Tuple[bool, str]:
    """
    Run a command and return success status and output.
    
    Args:
        command: Command to run as list of strings
        description: Description of what the command does
        
    Returns:
        Tuple of (success, output)
    """
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            check=False
        )
        
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True, result.stdout
        else:
            print(f"❌ {description} failed:")
            print(f"   Error: {result.stderr}")
            return False, result.stderr
            
    except Exception as e:
        print(f"❌ {description} failed with exception: {e}")
        return False, str(e)


def check_current_installations():
    """Check current TensorFlow and NumPy installations."""
    print("\n" + "="*60)
    print("🔍 CHECKING CURRENT INSTALLATIONS")
    print("="*60)
    
    # Check TensorFlow
    try:
        import tensorflow as tf
        print(f"📦 TensorFlow: {tf.__version__} (installed)")
        tf_installed = True
    except ImportError:
        print("📦 TensorFlow: Not installed")
        tf_installed = False
    except Exception as e:
        print(f"📦 TensorFlow: Error loading - {e}")
        tf_installed = True  # Installed but broken
    
    # Check NumPy
    try:
        import numpy as np
        print(f"📦 NumPy: {np.__version__} (installed)")
        numpy_version = np.__version__
    except ImportError:
        print("📦 NumPy: Not installed")
        numpy_version = None
    
    # Check PyPOTS
    try:
        import pypots
        print(f"📦 PyPOTS: {pypots.__version__} (installed)")
    except ImportError:
        print("📦 PyPOTS: Not installed")
    except Exception as e:
        print(f"📦 PyPOTS: Error loading - {e}")
    
    return tf_installed, numpy_version


def uninstall_tensorflow():
    """Uninstall all TensorFlow packages."""
    print("\n" + "="*60)
    print("🗑️ UNINSTALLING TENSORFLOW PACKAGES")
    print("="*60)
    
    packages_to_remove = [
        'tensorflow',
        'tensorflow-gpu', 
        'tensorflow-cpu',
        'tf-nightly',
        'tf-nightly-gpu',
        'tf-nightly-cpu'
    ]
    
    for package in packages_to_remove:
        success, output = run_command(
            [sys.executable, '-m', 'pip', 'uninstall', package, '-y'],
            f"Uninstalling {package}"
        )
        # Don't worry if package wasn't installed


def install_compatible_packages():
    """Install compatible TensorFlow and NumPy versions."""
    print("\n" + "="*60)
    print("📦 INSTALLING COMPATIBLE PACKAGES")
    print("="*60)
    
    # Install compatible NumPy first
    success, output = run_command(
        [sys.executable, '-m', 'pip', 'install', 'numpy>=1.21.0,<1.25.0'],
        "Installing compatible NumPy"
    )
    
    if not success:
        print("⚠️ NumPy installation failed, continuing anyway...")
    
    # Install TensorFlow CPU
    success, output = run_command(
        [sys.executable, '-m', 'pip', 'install', 'tensorflow-cpu>=2.10.0,<2.16.0'],
        "Installing TensorFlow CPU"
    )
    
    if not success:
        print("❌ TensorFlow CPU installation failed!")
        return False
    
    # Reinstall PyPOTS to ensure compatibility
    success, output = run_command(
        [sys.executable, '-m', 'pip', 'install', '--upgrade', 'pypots>=0.2.0'],
        "Upgrading PyPOTS"
    )
    
    return True


def test_installation():
    """Test the TensorFlow installation."""
    print("\n" + "="*60)
    print("🧪 TESTING INSTALLATION")
    print("="*60)
    
    try:
        # Test TensorFlow import
        print("🔍 Testing TensorFlow import...")
        import tensorflow as tf
        print(f"✅ TensorFlow {tf.__version__} imported successfully")
        
        # Test basic operation
        print("🔍 Testing basic TensorFlow operation...")
        x = tf.constant([1, 2, 3, 4])
        y = tf.constant([2, 3, 4, 5])
        result = tf.add(x, y)
        print(f"✅ Basic operation successful: {result.numpy()}")
        
        # Test PyPOTS import
        print("🔍 Testing PyPOTS import...")
        from pypots.imputation import SAITS, BRITS
        from pypots.optim import Adam
        print("✅ PyPOTS components imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False


def configure_environment():
    """Configure environment variables for optimal performance."""
    print("\n" + "="*60)
    print("⚙️ CONFIGURING ENVIRONMENT")
    print("="*60)
    
    env_vars = {
        'TF_ENABLE_ONEDNN_OPTS': '0',
        'TF_CPP_MIN_LOG_LEVEL': '2',
        'CUDA_VISIBLE_DEVICES': '-1',  # Force CPU-only
        'TF_FORCE_GPU_ALLOW_GROWTH': 'true'
    }
    
    for var, value in env_vars.items():
        os.environ[var] = value
        print(f"✅ Set {var}={value}")
    
    print("✅ Environment configured for CPU-only TensorFlow")


def main():
    """Main fix procedure."""
    print("="*80)
    print("🔧 TENSORFLOW DLL FIX UTILITY")
    print("="*80)
    print("This script will fix TensorFlow DLL loading issues by:")
    print("1. Uninstalling problematic TensorFlow packages")
    print("2. Installing compatible TensorFlow CPU and NumPy versions")
    print("3. Testing the installation")
    print("4. Configuring environment variables")
    print("="*80)
    
    # Check current state
    tf_installed, numpy_version = check_current_installations()
    
    # Ask for confirmation
    response = input("\n🤔 Do you want to proceed with the fix? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Fix cancelled by user")
        return
    
    # Step 1: Uninstall TensorFlow
    uninstall_tensorflow()
    
    # Step 2: Install compatible packages
    if not install_compatible_packages():
        print("\n❌ Package installation failed. Please check your internet connection and try again.")
        return
    
    # Step 3: Configure environment
    configure_environment()
    
    # Step 4: Test installation
    if test_installation():
        print("\n" + "="*80)
        print("🎉 TENSORFLOW DLL FIX COMPLETED SUCCESSFULLY!")
        print("="*80)
        print("✅ TensorFlow CPU installed and working")
        print("✅ PyPOTS should now work correctly")
        print("✅ Environment configured for optimal performance")
        print("\n💡 You can now run your ML Log Prediction application")
        print("="*80)
    else:
        print("\n" + "="*80)
        print("❌ INSTALLATION TEST FAILED")
        print("="*80)
        print("The packages were installed but there may still be issues.")
        print("Please check the error messages above and consider:")
        print("1. Restarting your Python environment")
        print("2. Installing Microsoft Visual C++ Redistributable")
        print("3. Checking for Windows updates")
        print("="*80)


if __name__ == "__main__":
    main()
