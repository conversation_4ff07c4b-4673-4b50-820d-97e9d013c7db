"""
TensorFlow Compatibility Module

This module provides compatibility fixes and fallback mechanisms for TensorFlow
import issues, particularly on Windows systems with DLL loading problems.

Key Features:
- Graceful TensorFlow import handling
- CPU-only fallback for DLL issues
- PyPOTS compatibility layer
- Environment detection and fixes

Author: ML Log Prediction System
Date: 2025-08-19
"""

import warnings
import os
import sys
from typing import Optional, Dict, Any, Tuple

# Global flags for TensorFlow availability
TENSORFLOW_AVAILABLE = False
TENSORFLOW_GPU_AVAILABLE = False
TENSORFLOW_VERSION = None
_TENSORFLOW_CHECKED = False  # Flag to prevent repeated checks

def setup_tensorflow_environment():
    """
    Set up TensorFlow environment variables to avoid common issues.
    """
    # Disable oneDNN optimizations that can cause issues
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
    
    # Disable GPU if CUDA is not properly configured
    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # Force CPU-only
    
    # Suppress TensorFlow warnings
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
    
    # Set memory growth for GPU (if available)
    os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'
    
    print("🔧 TensorFlow environment configured for compatibility")


def import_tensorflow_safe() -> Tuple[bool, Optional[Any], Optional[str]]:
    """
    Safely import TensorFlow with fallback mechanisms.

    Returns:
        Tuple of (success, tensorflow_module, error_message)
    """
    global TENSORFLOW_AVAILABLE, TENSORFLOW_GPU_AVAILABLE, TENSORFLOW_VERSION, _TENSORFLOW_CHECKED

    # Return cached result if already checked
    if _TENSORFLOW_CHECKED:
        if TENSORFLOW_AVAILABLE:
            import tensorflow as tf
            return True, tf, None
        else:
            return False, None, "TensorFlow not available (cached result)"

    # Set up environment first
    setup_tensorflow_environment()

    try:
        # Try importing TensorFlow
        import tensorflow as tf
        TENSORFLOW_AVAILABLE = True
        TENSORFLOW_VERSION = tf.__version__
        _TENSORFLOW_CHECKED = True

        # Check GPU availability
        try:
            gpus = tf.config.list_physical_devices('GPU')
            TENSORFLOW_GPU_AVAILABLE = len(gpus) > 0
            if TENSORFLOW_GPU_AVAILABLE:
                print(f"✅ TensorFlow {TENSORFLOW_VERSION} loaded with GPU support")
            else:
                print(f"✅ TensorFlow {TENSORFLOW_VERSION} loaded (CPU-only)")
        except Exception:
            TENSORFLOW_GPU_AVAILABLE = False
            print(f"✅ TensorFlow {TENSORFLOW_VERSION} loaded (CPU-only)")

        return True, tf, None

    except ImportError as e:
        _TENSORFLOW_CHECKED = True
        error_msg = str(e)

        # Check for specific DLL loading issues (only show once)
        if "DLL load failed" in error_msg or "_pywrap_tensorflow_internal" in error_msg:
            print("⚠️ TensorFlow DLL loading failed - this is a common Windows issue")
            print("💡 Suggestions:")
            print("   1. Install Microsoft Visual C++ Redistributable")
            print("   2. Use tensorflow-cpu instead of tensorflow")
            print("   3. Update to compatible numpy version")
            print("   4. Run: python fix_tensorflow_dll.py")

        elif "numpy" in error_msg and "dtypes" in error_msg:
            print("⚠️ TensorFlow-NumPy compatibility issue detected")
            print("💡 Solution: Update numpy to compatible version")
            print("   Run: pip install 'numpy>=1.21.0,<1.25.0'")

        return False, None, error_msg

    except Exception as e:
        _TENSORFLOW_CHECKED = True
        error_msg = f"Unexpected TensorFlow import error: {str(e)}"
        return False, None, error_msg


def import_pypots_safe() -> Tuple[bool, Optional[Any], Optional[str]]:
    """
    Safely import PyPOTS with TensorFlow compatibility handling.
    
    Returns:
        Tuple of (success, pypots_modules, error_message)
    """
    try:
        # First ensure TensorFlow is available
        tf_success, tf_module, tf_error = import_tensorflow_safe()
        
        if not tf_success:
            return False, None, f"PyPOTS requires TensorFlow: {tf_error}"
        
        # Try importing PyPOTS components
        from pypots.imputation import SAITS, BRITS
        from pypots.optim import Adam
        
        pypots_modules = {
            'SAITS': SAITS,
            'BRITS': BRITS,
            'Adam': Adam
        }
        
        print("✅ PyPOTS imported successfully with TensorFlow backend")
        return True, pypots_modules, None
        
    except ImportError as e:
        error_msg = f"PyPOTS import failed: {str(e)}"
        return False, None, error_msg
    except Exception as e:
        error_msg = f"Unexpected PyPOTS import error: {str(e)}"
        return False, None, error_msg


def get_tensorflow_status() -> Dict[str, Any]:
    """
    Get current TensorFlow status and configuration.
    
    Returns:
        Dictionary with TensorFlow status information
    """
    return {
        'tensorflow_available': TENSORFLOW_AVAILABLE,
        'tensorflow_gpu_available': TENSORFLOW_GPU_AVAILABLE,
        'tensorflow_version': TENSORFLOW_VERSION,
        'environment_configured': True
    }


def create_tensorflow_fallback():
    """
    Create fallback implementations for when TensorFlow is not available.
    """
    class MockTensorFlow:
        """Mock TensorFlow for fallback scenarios."""
        __version__ = "mock-fallback"
        
        @staticmethod
        def config():
            return MockConfig()
    
    class MockConfig:
        """Mock TensorFlow config."""
        @staticmethod
        def list_physical_devices(device_type='GPU'):
            return []
    
    return MockTensorFlow()


def install_tensorflow_fix():
    """
    Provide installation instructions for fixing TensorFlow issues.
    """
    print("\n" + "="*60)
    print("🔧 TENSORFLOW INSTALLATION FIX")
    print("="*60)
    print("\nTo fix TensorFlow DLL loading issues on Windows:")
    print("\n1. Uninstall current TensorFlow:")
    print("   pip uninstall tensorflow tensorflow-gpu")
    print("\n2. Install CPU-only version:")
    print("   pip install tensorflow-cpu>=2.10.0,<2.16.0")
    print("\n3. Fix NumPy compatibility:")
    print("   pip install 'numpy>=1.21.0,<1.25.0'")
    print("\n4. Install Visual C++ Redistributable:")
    print("   Download from Microsoft's official website")
    print("\n5. Restart your Python environment")
    print("\n" + "="*60)


# Initialize TensorFlow on module import (with suppressed warnings for repeated imports)
if __name__ != "__main__":
    # Only run initialization if not being executed directly
    tf_success, tf_module, tf_error = import_tensorflow_safe()
    if not tf_success and tf_error and not _TENSORFLOW_CHECKED:
        # Only warn once, not on repeated imports
        warnings.warn(f"TensorFlow initialization failed: {tf_error}")
