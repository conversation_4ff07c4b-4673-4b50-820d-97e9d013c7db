   [OK] PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\preprocessing\deep_model\phase1_preprocessing.py:56: UserWarning: Original ml_core functions not available. Some fallback functionality may be limited.
  warnings.warn("Original ml_core functions not available. Some fallback functionality may be limited.") 
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\preprocessing\deep_model\phase1_integration.py:59: UserWarning: Original ml_core functions not available. Some fallback functionality may be limited.
  warnings.warn("Original ml_core functions not available. Some fallback functionality may be limited.") 
Multiple Linear Regression utilities loaded successfully
Data leakage detection module loaded
Loading advanced deep learning models...
WARNING: Failed to find MSVC.
WARNING: Failed to find Windows SDK.
WARNING: Failed to find CUDA.
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\models\advanced_models\saits_model.py:21: UserWarning: PyPOTS not available: Traceback (most recent call last):
  File "C:\Users\<USER>\mwlt\lib\site-packages\tensorflow\python\pywrap_tensorflow.py", line 73, in <module>
    from tensorflow.python._pywrap_tensorflow_internal import *
ImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.


Failed to load the native TensorFlow runtime.
See https://www.tensorflow.org/install/errors for some common causes and solutions.
If you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.
  warnings.warn(f"PyPOTS not available: {e}")
SAITS model loaded successfully
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\models\advanced_models\brits_model.py:21: UserWarning: PyPOTS not available: Traceback (most recent call last):
  File "C:\Users\<USER>\mwlt\lib\site-packages\tensorflow\python\pywrap_tensorflow.py", line 73, in <module>
    from tensorflow.python._pywrap_tensorflow_internal import *
ImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.


Failed to load the native TensorFlow runtime.
See https://www.tensorflow.org/install/errors for some common causes and solutions.
If you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.
  warnings.warn(f"PyPOTS not available: {e}")
BRITS model loaded successfully
Advanced models loaded: ['saits', 'brits']
Total available: 2/5 models
Advanced models module initialized (Phase 1 foundation)
Ready for Phase 2: Core model implementations
Advanced deep learning models module loaded
Available advanced models: ['saits', 'brits']
SAITS model added to registry
BRITS model added to registry
Enhanced MODEL_REGISTRY with 2 advanced models
Available advanced models: ['saits', 'brits']
   [OK] Phase 1 Enhanced Deep Learning Integration loaded
   [OK] Optimized pipeline functions available
INFO:utils.display_utils:Configuring fonts for Windows system
INFO:utils.display_utils:Set matplotlib font to: Segoe UI Emoji
INFO:utils.display_utils:Emoji support confirmed
INFO:utils.display_utils:Configured warning filters for font issues
🔍 Performance Monitor initialized
   • GPU monitoring enabled
   • Monitoring interval: 1.0s
[MEM] Environment configured for memory optimization
[MEM] Memory Optimizer initialized
   * Mixed precision enabled
   * Memory monitoring enabled
   [OK] Memory optimizer initialized
============================================================
 ML LOG PREDICTION
============================================================

[MEM] Initial Memory Status:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 13.5 GB
   * Usage: 57.3%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.0 GB
   * Reserved: 0.0 GB
   * Free: 4.0 GB
==================================================

Step 1: Select LAS files
Select LAS files using the file dialog...
No files selected.
No files selected. Exiting.
File selection cancelled. Exiting.