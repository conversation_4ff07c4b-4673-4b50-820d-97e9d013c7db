"""Multiple Linear Regression Preprocessing Module

This module provides specialized preprocessing utilities for implementing multiple linear regression (MLR)
in well log imputation workflows. It includes preprocessing, diagnostic tools, and feature scaling
that integrate seamlessly with the existing ML pipeline.

Key Features:
- Automated preprocessing (scaling, outlier detection)
- Multicollinearity detection using Variance Inflation Factor (VIF)
- Linear regression assumption validation
- Diagnostic plotting and statistical analysis
- Robust error handling with graceful fallbacks

Author: ML Log Prediction System
"""

import numpy as np
import pandas as pd
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
import scipy.stats as stats

# Optional dependencies with graceful fallbacks
try:
    from statsmodels.stats.outliers_influence import variance_inflation_factor
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    warnings.warn("Statsmodels not available. VIF calculation will be skipped.")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    warnings.warn("Matplotlib/Seaborn not available. Diagnostic plots will be skipped.")


class MLRPreprocessor:
    """Preprocessor for Multiple Linear Regression models.
    
    Handles feature scaling, outlier detection, and multicollinearity analysis
    specifically designed for well log data characteristics.
    """
    
    def __init__(self, 
                 scaling_method: str = 'standard',
                 outlier_threshold: float = 3.0,
                 vif_threshold: float = 10.0,
                 handle_outliers: bool = True,
                 enable_diagnostics: bool = False):
        """Initialize MLR preprocessor.
        
        Args:
            scaling_method: Method for feature scaling ('standard', 'robust', 'minmax')
            outlier_threshold: Z-score threshold for outlier detection
            vif_threshold: VIF threshold for multicollinearity detection
            handle_outliers: Whether to remove/cap outliers
            enable_diagnostics: Whether to generate diagnostic output
        """
        self.scaling_method = scaling_method
        self.outlier_threshold = outlier_threshold
        self.vif_threshold = vif_threshold
        self.handle_outliers = handle_outliers
        self.enable_diagnostics = enable_diagnostics
        
        # Initialize scaler based on method
        if scaling_method == 'standard':
            self.scaler = StandardScaler()
        elif scaling_method == 'robust':
            self.scaler = RobustScaler()
        elif scaling_method == 'minmax':
            self.scaler = MinMaxScaler()
        else:
            raise ValueError(f"Unknown scaling method: {scaling_method}")
        
        self.is_fitted = False
        self.feature_names = None
        self.outlier_mask = None
        self.vif_values = None
        self.diagnostics = {}
    
    def fit(self, X: pd.DataFrame, y: pd.Series = None) -> 'MLRPreprocessor':
        """Fit the preprocessor to the training data.
        
        Args:
            X: Feature matrix
            y: Target vector (optional, for diagnostics)
            
        Returns:
            Self for method chaining
        """
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        self.feature_names = list(X.columns)
        
        # Handle outliers if requested
        if self.handle_outliers:
            self.outlier_mask = self._detect_outliers(X)
            X_clean = X[~self.outlier_mask]
            if self.enable_diagnostics:
                outlier_count = self.outlier_mask.sum()
                print(f"🔍 MLR Preprocessing: Detected {outlier_count} outliers ({outlier_count/len(X)*100:.1f}%)")
        else:
            X_clean = X
            self.outlier_mask = pd.Series(False, index=X.index)
        
        # Fit scaler
        self.scaler.fit(X_clean)
        
        # Calculate VIF if available
        if STATSMODELS_AVAILABLE and len(X_clean.columns) > 1:
            try:
                X_scaled = pd.DataFrame(
                    self.scaler.transform(X_clean),
                    columns=X_clean.columns,
                    index=X_clean.index
                )
                self.vif_values = self._calculate_vif(X_scaled)
                
                if self.enable_diagnostics:
                    self._print_vif_diagnostics()
                    
            except Exception as e:
                if self.enable_diagnostics:
                    print(f"⚠️ VIF calculation failed: {e}")
                self.vif_values = None
        
        self.is_fitted = True
        return self
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """Transform features using fitted preprocessor.
        
        Args:
            X: Feature matrix to transform
            
        Returns:
            Transformed feature matrix
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before transform")
        
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        
        # Apply scaling
        X_scaled = pd.DataFrame(
            self.scaler.transform(X),
            columns=X.columns,
            index=X.index
        )
        
        return X_scaled
    
    def fit_transform(self, X: pd.DataFrame, y: pd.Series = None) -> pd.DataFrame:
        """Fit preprocessor and transform data in one step.
        
        Args:
            X: Feature matrix
            y: Target vector (optional)
            
        Returns:
            Transformed feature matrix
        """
        return self.fit(X, y).transform(X)
    
    def _detect_outliers(self, X: pd.DataFrame) -> pd.Series:
        """Detect outliers using Z-score method.
        
        Args:
            X: Feature matrix
            
        Returns:
            Boolean mask indicating outliers
        """
        z_scores = np.abs(stats.zscore(X, nan_policy='omit'))
        outlier_mask = (z_scores > self.outlier_threshold).any(axis=1)
        return outlier_mask
    
    def _calculate_vif(self, X: pd.DataFrame) -> Dict[str, float]:
        """Calculate Variance Inflation Factor for each feature.
        
        Args:
            X: Scaled feature matrix
            
        Returns:
            Dictionary mapping feature names to VIF values
        """
        vif_data = {}
        
        for i, feature in enumerate(X.columns):
            try:
                vif_value = variance_inflation_factor(X.values, i)
                vif_data[feature] = vif_value
            except Exception as e:
                if self.enable_diagnostics:
                    print(f"⚠️ VIF calculation failed for {feature}: {e}")
                vif_data[feature] = np.nan
        
        return vif_data
    
    def _print_vif_diagnostics(self):
        """Print VIF diagnostic information."""
        if self.vif_values is None:
            return
        
        print("\n📊 Multicollinearity Analysis (VIF):")
        print("   Feature                VIF      Status")
        print("   " + "-" * 40)
        
        for feature, vif in self.vif_values.items():
            if np.isnan(vif):
                status = "Error"
            elif vif > self.vif_threshold:
                status = "High ⚠️"
            elif vif > 5.0:
                status = "Moderate"
            else:
                status = "Low ✓"
            
            print(f"   {feature:<20} {vif:>6.2f}    {status}")
        
        high_vif_features = [f for f, v in self.vif_values.items() 
                           if not np.isnan(v) and v > self.vif_threshold]
        
        if high_vif_features:
            print(f"\n   ⚠️ High multicollinearity detected in: {', '.join(high_vif_features)}")
            print(f"   Consider using Ridge or ElasticNet regression.")
        else:
            print(f"\n   ✅ No severe multicollinearity detected.")


class MLRModelWrapper:
    """Wrapper for MLR models with preprocessing integration."""
    
    def __init__(self, model, preprocessor=None):
        self.model = model
        self.preprocessor = preprocessor or MLRPreprocessor()
        self.is_fitted = False
    
    def fit(self, X, y):
        """Fit the model with preprocessing."""
        X_processed = self.preprocessor.fit_transform(X, y)
        self.model.fit(X_processed, y)
        self.is_fitted = True
        return self
    
    def predict(self, X):
        """Make predictions with preprocessing."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        X_processed = self.preprocessor.transform(X)
        return self.model.predict(X_processed)


def create_mlr_model(model_class, **kwargs):
    """Create an MLR model with preprocessing."""
    model = model_class(**kwargs)
    return MLRModelWrapper(model)


def validate_mlr_assumptions(X: pd.DataFrame, y: pd.Series, 
                           model=None,
                           enable_plots: bool = False) -> Dict[str, Any]:
    """Validate linear regression assumptions.
    
    Args:
        X: Feature matrix
        y: Target vector
        model: Fitted MLR model (optional)
        enable_plots: Whether to generate diagnostic plots
        
    Returns:
        Dictionary with assumption validation results
    """
    results = {
        'linearity': {'status': 'unknown', 'details': {}},
        'independence': {'status': 'unknown', 'details': {}},
        'homoscedasticity': {'status': 'unknown', 'details': {}},
        'normality': {'status': 'unknown', 'details': {}},
        'multicollinearity': {'status': 'unknown', 'details': {}}
    }
    
    try:
        # If model is provided, use it for residual analysis
        if model is not None and hasattr(model, 'is_fitted') and model.is_fitted:
            y_pred = model.predict(X)
            residuals = y - y_pred
            
            # Homoscedasticity test (Breusch-Pagan test would be ideal)
            # For now, use simple variance analysis
            residual_std = np.std(residuals)
            results['homoscedasticity']['details']['residual_std'] = residual_std
            
            # Normality test for residuals
            if len(residuals) > 3:
                _, p_value = stats.shapiro(residuals[:min(5000, len(residuals))])
                results['normality']['status'] = 'pass' if p_value > 0.05 else 'fail'
                results['normality']['details']['shapiro_p_value'] = p_value
        
        # Multicollinearity check
        if STATSMODELS_AVAILABLE and len(X.columns) > 1:
            preprocessor = MLRPreprocessor(enable_diagnostics=False)
            X_scaled = preprocessor.fit_transform(X)
            vif_values = preprocessor._calculate_vif(X_scaled)
            
            max_vif = max([v for v in vif_values.values() if not np.isnan(v)], default=0)
            results['multicollinearity']['status'] = 'pass' if max_vif < 10 else 'fail'
            results['multicollinearity']['details']['vif_values'] = vif_values
            results['multicollinearity']['details']['max_vif'] = max_vif
        
        return results
        
    except Exception as e:
        print(f"⚠️ Assumption validation failed: {e}")
        return results