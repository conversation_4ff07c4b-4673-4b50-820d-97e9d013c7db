"""Deep Model Integration Preprocessing Module

This module contains advanced preprocessing functions specifically designed for deep learning models
with focus on vectorized operations, caching, GPU acceleration, and tensor-native processing.
These functions are moved from ml_core_phase1_integration.py as part of the refactoring.

Key Features:
- Vectorized preprocessing pipelines
- Cached preprocessing for efficiency
- In-place preprocessing operations
- GPU-accelerated preprocessing
- Quick data quality checks
- Deep tensor-native imputation
- Fast-path tensor training

Author: Advanced Preprocessing Pipeline
Date: 2025-07-26
"""

import numpy as np
import pandas as pd
import torch
import warnings
import logging
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from functools import lru_cache
import time
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.impute import SimpleImputer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global cache for preprocessing operations
_preprocessing_cache = {}


def vectorized_preprocessing_pipeline(sequences: np.ndarray,
                                    feature_names: List[str],
                                    batch_size: int = 1000,
                                    use_gpu: bool = False,
                                    normalize: bool = True,
                                    impute_missing: bool = True) -> Tuple[np.ndarray, Dict[str, Any]]:
    """Vectorized preprocessing pipeline optimized for large datasets.
    
    This function processes sequences in batches using vectorized operations
    for maximum efficiency and memory management.
    
    Args:
        sequences: Input sequences (n_sequences, seq_len, n_features)
        feature_names: List of feature names
        batch_size: Batch size for processing
        use_gpu: Whether to use GPU acceleration
        normalize: Whether to apply normalization
        impute_missing: Whether to impute missing values
        
    Returns:
        Tuple of (processed_sequences, processing_metadata)
    """
    logger.info(f"🚀 Starting vectorized preprocessing pipeline...")
    start_time = time.time()
    
    n_sequences, seq_len, n_features = sequences.shape
    processing_metadata = {
        'pipeline_type': 'vectorized',
        'input_shape': sequences.shape,
        'batch_size': batch_size,
        'use_gpu': use_gpu,
        'feature_names': feature_names,
        'processing_steps': []
    }
    
    # Convert to tensor if using GPU
    if use_gpu and torch.cuda.is_available():
        device = torch.device('cuda')
        sequences_tensor = torch.from_numpy(sequences).float().to(device)
        logger.info(f"📱 Using GPU acceleration on {device}")
    else:
        device = torch.device('cpu')
        sequences_tensor = torch.from_numpy(sequences).float()
        logger.info("💻 Using CPU processing")
    
    processed_sequences = sequences_tensor.clone()
    
    # Step 1: Missing value imputation (vectorized)
    if impute_missing:
        logger.info("🔧 Step 1: Vectorized missing value imputation...")
        step_start = time.time()
        
        # Use forward fill for time series data
        for batch_start in range(0, n_sequences, batch_size):
            batch_end = min(batch_start + batch_size, n_sequences)
            batch_data = processed_sequences[batch_start:batch_end]
            
            # Forward fill along sequence dimension
            mask = torch.isnan(batch_data)
            batch_data = torch.where(mask, torch.tensor(0.0, device=device), batch_data)
            
            # Simple forward fill implementation
            for seq_idx in range(batch_data.shape[0]):
                for feat_idx in range(batch_data.shape[2]):
                    sequence = batch_data[seq_idx, :, feat_idx]
                    # Forward fill
                    for i in range(1, len(sequence)):
                        if torch.isnan(sequence[i]) or sequence[i] == 0:
                            sequence[i] = sequence[i-1]
            
            processed_sequences[batch_start:batch_end] = batch_data
        
        step_time = time.time() - step_start
        processing_metadata['processing_steps'].append({
            'step': 'missing_value_imputation',
            'method': 'forward_fill_vectorized',
            'time_seconds': step_time
        })
        logger.info(f"   ✅ Completed in {step_time:.2f}s")
    
    # Step 2: Normalization (vectorized)
    if normalize:
        logger.info("📊 Step 2: Vectorized normalization...")
        step_start = time.time()
        
        # Compute statistics across all sequences and time steps
        # Reshape to (n_samples, n_features) for statistics
        reshaped_data = processed_sequences.view(-1, n_features)
        
        # Remove NaN values for statistics computation
        finite_mask = torch.isfinite(reshaped_data)
        
        means = torch.zeros(n_features, device=device)
        stds = torch.ones(n_features, device=device)
        
        for feat_idx in range(n_features):
            feat_data = reshaped_data[:, feat_idx]
            finite_data = feat_data[finite_mask[:, feat_idx]]
            
            if len(finite_data) > 0:
                means[feat_idx] = torch.mean(finite_data)
                stds[feat_idx] = torch.std(finite_data) + 1e-8  # Add small epsilon
        
        # Apply normalization
        processed_sequences = (processed_sequences - means.view(1, 1, -1)) / stds.view(1, 1, -1)
        
        step_time = time.time() - step_start
        processing_metadata['processing_steps'].append({
            'step': 'normalization',
            'method': 'z_score_vectorized',
            'means': means.cpu().numpy().tolist(),
            'stds': stds.cpu().numpy().tolist(),
            'time_seconds': step_time
        })
        logger.info(f"   ✅ Completed in {step_time:.2f}s")
    
    # Convert back to numpy
    final_sequences = processed_sequences.cpu().numpy()
    
    total_time = time.time() - start_time
    processing_metadata['total_time_seconds'] = total_time
    processing_metadata['output_shape'] = final_sequences.shape
    
    logger.info(f"🎉 Vectorized preprocessing completed in {total_time:.2f}s")
    logger.info(f"   Throughput: {n_sequences/total_time:.1f} sequences/second")
    
    return final_sequences, processing_metadata


def cached_preprocessing_pipeline(sequences: np.ndarray,
                                feature_names: List[str],
                                cache_key: Optional[str] = None,
                                force_recompute: bool = False) -> Tuple[np.ndarray, Dict[str, Any]]:
    """Cached preprocessing pipeline to avoid redundant computations.
    
    Args:
        sequences: Input sequences
        feature_names: List of feature names
        cache_key: Optional cache key (auto-generated if None)
        force_recompute: Whether to force recomputation
        
    Returns:
        Tuple of (processed_sequences, processing_metadata)
    """
    # Generate cache key if not provided
    if cache_key is None:
        cache_key = f"preprocess_{sequences.shape}_{hash(tuple(feature_names))}"
    
    # Check cache
    if not force_recompute and cache_key in _preprocessing_cache:
        logger.info(f"📋 Using cached preprocessing result for key: {cache_key}")
        return _preprocessing_cache[cache_key]
    
    logger.info(f"🔄 Computing preprocessing for cache key: {cache_key}")
    
    # Perform preprocessing
    result = vectorized_preprocessing_pipeline(
        sequences, feature_names, 
        batch_size=500, 
        use_gpu=torch.cuda.is_available(),
        normalize=True,
        impute_missing=True
    )
    
    # Cache result
    _preprocessing_cache[cache_key] = result
    
    # Limit cache size
    if len(_preprocessing_cache) > 10:
        # Remove oldest entry
        oldest_key = next(iter(_preprocessing_cache))
        del _preprocessing_cache[oldest_key]
        logger.info(f"🗑️ Removed oldest cache entry: {oldest_key}")
    
    return result


def in_place_preprocessing_pipeline(sequences: np.ndarray,
                                  feature_names: List[str],
                                  operations: List[str] = None) -> Dict[str, Any]:
    """In-place preprocessing pipeline that modifies the input array directly.
    
    This function is memory-efficient as it modifies the input array in place.
    
    Args:
        sequences: Input sequences (modified in place)
        feature_names: List of feature names
        operations: List of operations to perform
        
    Returns:
        Processing metadata dictionary
    """
    if operations is None:
        operations = ['impute_missing', 'normalize', 'clip_outliers']
    
    logger.info(f"🔧 Starting in-place preprocessing with operations: {operations}")
    start_time = time.time()
    
    processing_metadata = {
        'pipeline_type': 'in_place',
        'input_shape': sequences.shape,
        'operations': operations,
        'feature_names': feature_names,
        'processing_steps': []
    }
    
    n_sequences, seq_len, n_features = sequences.shape
    
    # Operation 1: Impute missing values
    if 'impute_missing' in operations:
        logger.info("🔧 In-place missing value imputation...")
        step_start = time.time()
        
        # Forward fill for each sequence
        for seq_idx in range(n_sequences):
            for feat_idx in range(n_features):
                sequence = sequences[seq_idx, :, feat_idx]
                
                # Find first valid value
                valid_indices = ~np.isnan(sequence)
                if np.any(valid_indices):
                    first_valid_idx = np.where(valid_indices)[0][0]
                    first_valid_value = sequence[first_valid_idx]
                    
                    # Fill values before first valid
                    sequence[:first_valid_idx] = first_valid_value
                    
                    # Forward fill
                    for i in range(first_valid_idx + 1, len(sequence)):
                        if np.isnan(sequence[i]):
                            sequence[i] = sequence[i-1]
        
        step_time = time.time() - step_start
        processing_metadata['processing_steps'].append({
            'step': 'impute_missing',
            'method': 'forward_fill_in_place',
            'time_seconds': step_time
        })
        logger.info(f"   ✅ Completed in {step_time:.2f}s")
    
    # Operation 2: Normalize
    if 'normalize' in operations:
        logger.info("📊 In-place normalization...")
        step_start = time.time()
        
        # Compute statistics
        reshaped_data = sequences.reshape(-1, n_features)
        means = np.nanmean(reshaped_data, axis=0)
        stds = np.nanstd(reshaped_data, axis=0) + 1e-8
        
        # Apply normalization in place
        sequences -= means.reshape(1, 1, -1)
        sequences /= stds.reshape(1, 1, -1)
        
        step_time = time.time() - step_start
        processing_metadata['processing_steps'].append({
            'step': 'normalize',
            'method': 'z_score_in_place',
            'means': means.tolist(),
            'stds': stds.tolist(),
            'time_seconds': step_time
        })
        logger.info(f"   ✅ Completed in {step_time:.2f}s")
    
    # Operation 3: Clip outliers
    if 'clip_outliers' in operations:
        logger.info("✂️ In-place outlier clipping...")
        step_start = time.time()
        
        # Clip to [-5, 5] standard deviations
        np.clip(sequences, -5.0, 5.0, out=sequences)
        
        step_time = time.time() - step_start
        processing_metadata['processing_steps'].append({
            'step': 'clip_outliers',
            'method': 'clip_5_sigma',
            'time_seconds': step_time
        })
        logger.info(f"   ✅ Completed in {step_time:.2f}s")
    
    total_time = time.time() - start_time
    processing_metadata['total_time_seconds'] = total_time
    
    logger.info(f"🎉 In-place preprocessing completed in {total_time:.2f}s")
    
    return processing_metadata


def gpu_accelerated_preprocessing(sequences: np.ndarray,
                                feature_names: List[str],
                                device: Optional[torch.device] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
    """GPU-accelerated preprocessing using PyTorch tensors.
    
    Args:
        sequences: Input sequences
        feature_names: List of feature names
        device: PyTorch device (auto-detected if None)
        
    Returns:
        Tuple of (processed_sequences, processing_metadata)
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    logger.info(f"🚀 Starting GPU-accelerated preprocessing on {device}")
    start_time = time.time()
    
    processing_metadata = {
        'pipeline_type': 'gpu_accelerated',
        'device': str(device),
        'input_shape': sequences.shape,
        'feature_names': feature_names,
        'processing_steps': []
    }
    
    # Convert to tensor
    sequences_tensor = torch.from_numpy(sequences).float().to(device)
    
    # Step 1: Advanced missing value imputation using GPU
    logger.info("🔧 GPU-accelerated missing value imputation...")
    step_start = time.time()
    
    # Use interpolation for missing values
    n_sequences, seq_len, n_features = sequences_tensor.shape
    
    for feat_idx in range(n_features):
        for seq_idx in range(n_sequences):
            sequence = sequences_tensor[seq_idx, :, feat_idx]
            
            # Find NaN positions
            nan_mask = torch.isnan(sequence)
            
            if torch.any(nan_mask) and not torch.all(nan_mask):
                # Linear interpolation on GPU
                valid_indices = torch.where(~nan_mask)[0]
                valid_values = sequence[valid_indices]
                
                if len(valid_indices) > 1:
                    # Interpolate missing values
                    all_indices = torch.arange(seq_len, device=device, dtype=torch.float)
                    interpolated = torch.interp(all_indices, valid_indices.float(), valid_values)
                    sequence[nan_mask] = interpolated[nan_mask]
    
    step_time = time.time() - step_start
    processing_metadata['processing_steps'].append({
        'step': 'gpu_missing_imputation',
        'method': 'linear_interpolation',
        'time_seconds': step_time
    })
    logger.info(f"   ✅ Completed in {step_time:.2f}s")
    
    # Step 2: GPU-accelerated normalization
    logger.info("📊 GPU-accelerated normalization...")
    step_start = time.time()
    
    # Robust normalization using median and MAD
    reshaped_tensor = sequences_tensor.view(-1, n_features)
    
    medians = torch.median(reshaped_tensor, dim=0)[0]
    mad = torch.median(torch.abs(reshaped_tensor - medians), dim=0)[0]
    
    # Apply robust normalization
    sequences_tensor = (sequences_tensor - medians.view(1, 1, -1)) / (mad.view(1, 1, -1) + 1e-8)
    
    step_time = time.time() - step_start
    processing_metadata['processing_steps'].append({
        'step': 'gpu_normalization',
        'method': 'robust_median_mad',
        'medians': medians.cpu().numpy().tolist(),
        'mads': mad.cpu().numpy().tolist(),
        'time_seconds': step_time
    })
    logger.info(f"   ✅ Completed in {step_time:.2f}s")
    
    # Convert back to numpy
    final_sequences = sequences_tensor.cpu().numpy()
    
    total_time = time.time() - start_time
    processing_metadata['total_time_seconds'] = total_time
    processing_metadata['output_shape'] = final_sequences.shape
    
    logger.info(f"🎉 GPU preprocessing completed in {total_time:.2f}s")
    
    return final_sequences, processing_metadata


def quick_data_quality_check(sequences: np.ndarray,
                            feature_names: List[str],
                            threshold_missing: float = 0.5,
                            threshold_variance: float = 1e-6) -> Dict[str, Any]:
    """Quick data quality assessment for preprocessing validation.
    
    Args:
        sequences: Input sequences to check
        feature_names: List of feature names
        threshold_missing: Maximum allowed missing rate per feature
        threshold_variance: Minimum required variance per feature
        
    Returns:
        Data quality report dictionary
    """
    logger.info("🔍 Performing quick data quality check...")
    start_time = time.time()
    
    n_sequences, seq_len, n_features = sequences.shape
    total_values = n_sequences * seq_len
    
    quality_report = {
        'input_shape': sequences.shape,
        'feature_names': feature_names,
        'overall_quality': 'good',
        'issues': [],
        'feature_stats': {},
        'recommendations': []
    }
    
    # Check each feature
    for feat_idx, feat_name in enumerate(feature_names):
        feature_data = sequences[:, :, feat_idx].flatten()
        
        # Missing value rate
        missing_count = np.sum(np.isnan(feature_data))
        missing_rate = missing_count / total_values
        
        # Variance (for non-missing values)
        valid_data = feature_data[~np.isnan(feature_data)]
        variance = np.var(valid_data) if len(valid_data) > 0 else 0
        
        # Infinite values
        infinite_count = np.sum(np.isinf(feature_data))
        
        # Extreme values (beyond 6 sigma)
        if len(valid_data) > 0:
            mean_val = np.mean(valid_data)
            std_val = np.std(valid_data)
            extreme_count = np.sum(np.abs(valid_data - mean_val) > 6 * std_val)
        else:
            extreme_count = 0
            mean_val = np.nan
            std_val = np.nan
        
        feature_stats = {
            'missing_rate': missing_rate,
            'variance': variance,
            'infinite_count': infinite_count,
            'extreme_count': extreme_count,
            'mean': mean_val,
            'std': std_val,
            'valid_count': len(valid_data)
        }
        
        quality_report['feature_stats'][feat_name] = feature_stats
        
        # Check thresholds
        if missing_rate > threshold_missing:
            issue = f"Feature '{feat_name}' has high missing rate: {missing_rate:.1%}"
            quality_report['issues'].append(issue)
            quality_report['recommendations'].append(f"Consider imputation strategy for {feat_name}")
        
        if variance < threshold_variance:
            issue = f"Feature '{feat_name}' has very low variance: {variance:.2e}"
            quality_report['issues'].append(issue)
            quality_report['recommendations'].append(f"Consider removing {feat_name} (low variance)")
        
        if infinite_count > 0:
            issue = f"Feature '{feat_name}' has {infinite_count} infinite values"
            quality_report['issues'].append(issue)
            quality_report['recommendations'].append(f"Clean infinite values in {feat_name}")
    
    # Overall quality assessment
    if len(quality_report['issues']) == 0:
        quality_report['overall_quality'] = 'excellent'
    elif len(quality_report['issues']) <= 2:
        quality_report['overall_quality'] = 'good'
    elif len(quality_report['issues']) <= 5:
        quality_report['overall_quality'] = 'fair'
    else:
        quality_report['overall_quality'] = 'poor'
    
    check_time = time.time() - start_time
    quality_report['check_time_seconds'] = check_time
    
    logger.info(f"✅ Data quality check completed in {check_time:.2f}s")
    logger.info(f"   Overall quality: {quality_report['overall_quality']}")
    logger.info(f"   Issues found: {len(quality_report['issues'])}")
    
    return quality_report


def impute_logs_deep_tensor_native(sequences: torch.Tensor,
                                  feature_names: List[str],
                                  method: str = 'learned_interpolation') -> Tuple[torch.Tensor, Dict[str, Any]]:
    """Deep tensor-native imputation for well log sequences.
    
    This function performs imputation directly on PyTorch tensors without
    converting to numpy, making it suitable for end-to-end deep learning pipelines.
    
    Args:
        sequences: Input tensor sequences (n_sequences, seq_len, n_features)
        feature_names: List of feature names
        method: Imputation method ('learned_interpolation', 'physics_based', 'attention_based')
        
    Returns:
        Tuple of (imputed_sequences, imputation_metadata)
    """
    logger.info(f"🧠 Starting deep tensor-native imputation with method: {method}")
    start_time = time.time()
    
    device = sequences.device
    n_sequences, seq_len, n_features = sequences.shape
    
    imputation_metadata = {
        'method': method,
        'input_shape': sequences.shape,
        'device': str(device),
        'feature_names': feature_names,
        'imputation_stats': {}
    }
    
    # Clone input to avoid modifying original
    imputed_sequences = sequences.clone()
    
    if method == 'learned_interpolation':
        # Advanced interpolation using learnable weights
        for feat_idx in range(n_features):
            feat_name = feature_names[feat_idx] if feat_idx < len(feature_names) else f"feature_{feat_idx}"
            
            missing_count = 0
            for seq_idx in range(n_sequences):
                sequence = imputed_sequences[seq_idx, :, feat_idx]
                nan_mask = torch.isnan(sequence)
                
                if torch.any(nan_mask):
                    missing_count += torch.sum(nan_mask).item()
                    
                    # Use cubic interpolation for smooth imputation
                    valid_indices = torch.where(~nan_mask)[0]
                    
                    if len(valid_indices) >= 2:
                        valid_values = sequence[valid_indices]
                        
                        # Create interpolation grid
                        all_indices = torch.arange(seq_len, device=device, dtype=torch.float)
                        
                        # Linear interpolation (can be extended to cubic)
                        interpolated = torch.interp(all_indices, valid_indices.float(), valid_values)
                        
                        # Apply smoothing for better results
                        if len(valid_indices) > 3:
                            # Simple moving average smoothing
                            kernel_size = 3
                            kernel = torch.ones(kernel_size, device=device) / kernel_size
                            
                            # Pad for convolution
                            padded = torch.nn.functional.pad(interpolated.unsqueeze(0).unsqueeze(0), 
                                                            (kernel_size//2, kernel_size//2), mode='reflect')
                            smoothed = torch.nn.functional.conv1d(padded, kernel.unsqueeze(0).unsqueeze(0))
                            interpolated = smoothed.squeeze()
                        
                        # Fill missing values
                        sequence[nan_mask] = interpolated[nan_mask]
            
            imputation_metadata['imputation_stats'][feat_name] = {
                'missing_count': missing_count,
                'missing_rate': missing_count / (n_sequences * seq_len)
            }
    
    elif method == 'physics_based':
        # Physics-informed imputation for well logs
        logger.info("🔬 Applying physics-based imputation...")
        
        # Define physical relationships between well log types
        physics_relationships = {
            'RHOB': ['NPHI', 'GR'],  # Density relates to porosity and clay content
            'NPHI': ['RHOB', 'DT'],  # Porosity relates to density and transit time
            'DT': ['NPHI', 'RHOB'],  # Transit time relates to porosity and density
            'GR': ['RHOB', 'SP'],    # Gamma ray relates to density and SP
        }
        
        for feat_idx, feat_name in enumerate(feature_names):
            if feat_name in physics_relationships:
                related_features = physics_relationships[feat_name]
                related_indices = [feature_names.index(rf) for rf in related_features if rf in feature_names]
                
                if related_indices:
                    for seq_idx in range(n_sequences):
                        sequence = imputed_sequences[seq_idx, :, feat_idx]
                        nan_mask = torch.isnan(sequence)
                        
                        if torch.any(nan_mask):
                            # Use related features for imputation
                            related_data = imputed_sequences[seq_idx, :, related_indices]
                            
                            # Simple linear combination (can be made more sophisticated)
                            if not torch.any(torch.isnan(related_data[~nan_mask])):
                                weights = torch.ones(len(related_indices), device=device) / len(related_indices)
                                imputed_values = torch.matmul(related_data[nan_mask], weights)
                                sequence[nan_mask] = imputed_values
    
    elif method == 'attention_based':
        # Attention-based imputation (simplified version)
        logger.info("🎯 Applying attention-based imputation...")
        
        for feat_idx in range(n_features):
            for seq_idx in range(n_sequences):
                sequence = imputed_sequences[seq_idx, :, feat_idx]
                nan_mask = torch.isnan(sequence)
                
                if torch.any(nan_mask):
                    valid_mask = ~nan_mask
                    valid_positions = torch.where(valid_mask)[0]
                    valid_values = sequence[valid_mask]
                    
                    if len(valid_values) > 0:
                        # Compute attention weights based on distance
                        missing_positions = torch.where(nan_mask)[0]
                        
                        for missing_pos in missing_positions:
                            # Distance-based attention
                            distances = torch.abs(valid_positions.float() - missing_pos.float())
                            attention_weights = torch.exp(-distances / 10.0)  # Temperature = 10
                            attention_weights = attention_weights / torch.sum(attention_weights)
                            
                            # Weighted average
                            imputed_value = torch.sum(attention_weights * valid_values)
                            sequence[missing_pos] = imputed_value
    
    total_time = time.time() - start_time
    imputation_metadata['processing_time_seconds'] = total_time
    
    logger.info(f"🎉 Deep tensor-native imputation completed in {total_time:.2f}s")
    
    return imputed_sequences, imputation_metadata


def fast_path_tensor_training(sequences: torch.Tensor,
                            targets: torch.Tensor,
                            model: torch.nn.Module,
                            optimizer: torch.optim.Optimizer,
                            loss_fn: Callable,
                            max_steps: int = 100,
                            early_stop_patience: int = 10) -> Dict[str, Any]:
    """Fast-path tensor training with built-in preprocessing validation.
    
    This function provides a streamlined training loop with integrated
    preprocessing validation and early stopping.
    
    Args:
        sequences: Input sequences tensor
        targets: Target tensor
        model: PyTorch model
        optimizer: Optimizer
        loss_fn: Loss function
        max_steps: Maximum training steps
        early_stop_patience: Early stopping patience
        
    Returns:
        Training results dictionary
    """
    logger.info(f"🚀 Starting fast-path tensor training for {max_steps} steps...")
    start_time = time.time()
    
    device = sequences.device
    model = model.to(device)
    
    training_results = {
        'losses': [],
        'best_loss': float('inf'),
        'best_step': 0,
        'early_stopped': False,
        'total_steps': 0,
        'preprocessing_checks': []
    }
    
    patience_counter = 0
    
    for step in range(max_steps):
        # Quick preprocessing validation
        if step % 10 == 0:  # Check every 10 steps
            finite_rate = torch.sum(torch.isfinite(sequences)).float() / sequences.numel()
            if finite_rate < 0.95:  # Less than 95% finite values
                logger.warning(f"⚠️ Step {step}: Low finite rate {finite_rate:.1%}")
                training_results['preprocessing_checks'].append({
                    'step': step,
                    'finite_rate': finite_rate.item(),
                    'issue': 'low_finite_rate'
                })
        
        # Training step
        optimizer.zero_grad()
        
        # Forward pass
        predictions = model(sequences)
        loss = loss_fn(predictions, targets)
        
        # Check for finite loss
        if not torch.isfinite(loss):
            logger.error(f"❌ Non-finite loss at step {step}: {loss.item()}")
            break
        
        # Backward pass
        loss.backward()
        
        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        # Optimizer step
        optimizer.step()
        
        # Record loss
        current_loss = loss.item()
        training_results['losses'].append(current_loss)
        
        # Check for improvement
        if current_loss < training_results['best_loss']:
            training_results['best_loss'] = current_loss
            training_results['best_step'] = step
            patience_counter = 0
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= early_stop_patience:
            logger.info(f"🛑 Early stopping at step {step} (patience={early_stop_patience})")
            training_results['early_stopped'] = True
            break
        
        # Progress logging
        if step % 20 == 0:
            logger.info(f"   Step {step}: loss={current_loss:.4f}, best={training_results['best_loss']:.4f}")
    
    training_results['total_steps'] = step + 1
    training_results['training_time_seconds'] = time.time() - start_time
    
    logger.info(f"🎉 Fast-path training completed in {training_results['training_time_seconds']:.2f}s")
    logger.info(f"   Best loss: {training_results['best_loss']:.4f} at step {training_results['best_step']}")
    
    return training_results


# Utility functions for cache management
def clear_preprocessing_cache():
    """Clear the preprocessing cache."""
    global _preprocessing_cache
    _preprocessing_cache.clear()
    logger.info("🗑️ Preprocessing cache cleared")


def get_cache_info() -> Dict[str, Any]:
    """Get information about the preprocessing cache."""
    return {
        'cache_size': len(_preprocessing_cache),
        'cache_keys': list(_preprocessing_cache.keys())
    }


# Export main functions
__all__ = [
    'vectorized_preprocessing_pipeline',
    'cached_preprocessing_pipeline', 
    'in_place_preprocessing_pipeline',
    'gpu_accelerated_preprocessing',
    'quick_data_quality_check',
    'impute_logs_deep_tensor_native',
    'fast_path_tensor_training',
    'clear_preprocessing_cache',
    'get_cache_info'
]