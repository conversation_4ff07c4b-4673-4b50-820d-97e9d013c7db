"""Deep model preprocessing module for ML Log Prediction.

This module contains preprocessing functionality specifically for deep learning models.
"""

# Import from stability_preprocessing
from .stability_preprocessing import (
    phase1_preprocessing_pipeline,
    enhanced_validate_sequences,
    get_recommended_preprocessing_config,
    generate_preprocessing_report,
    encode_missing_values,
    numerical_stability_check,
    UniversalGradientClipper,
    AdaptiveLRScheduler
)

# Import from phase1_preprocessing
from .phase1_preprocessing import (
    OptimizationConfig,
    SmartValidator,
    vectorized_preprocessing_pipeline,
    quick_data_quality_check,
    impute_logs_deep_tensor_native,
    direct_tensor_training,
    fast_path_tensor_training,
    parallel_feature_validation,
    get_data_hash,
    cached_preprocessing_pipeline,
    in_place_preprocessing_pipeline,
    gpu_accelerated_preprocessing,
    benchmark_optimization_performance,
    simulate_original_processing,
    configure_optimization_for_hardware
)

# Import from phase1_integration
from .phase1_integration import (
    impute_logs_deep_phase1_optimized
)

# Import from integration_preprocessing.py
from .integration_preprocessing import (
    vectorized_preprocessing_pipeline as integration_vectorized_preprocessing_pipeline,
    cached_preprocessing_pipeline,
    in_place_preprocessing_pipeline,
    gpu_accelerated_preprocessing,
    quick_data_quality_check,
    impute_logs_deep_tensor_native,
    fast_path_tensor_training,
    clear_preprocessing_cache,
    get_cache_info
)

__all__ = [
    # Stability preprocessing
    'phase1_preprocessing_pipeline',
    'enhanced_validate_sequences',
    'get_recommended_preprocessing_config',
    'generate_preprocessing_report',
    'encode_missing_values',
    'numerical_stability_check',
    'UniversalGradientClipper',
    'AdaptiveLRScheduler',
    
    # Phase 1 preprocessing
    'OptimizationConfig',
    'SmartValidator',
    'vectorized_preprocessing_pipeline',
    'quick_data_quality_check',
    'impute_logs_deep_tensor_native',
    'direct_tensor_training',
    'fast_path_tensor_training',
    'parallel_feature_validation',
    'get_data_hash',
    'cached_preprocessing_pipeline',
    'in_place_preprocessing_pipeline',
    'gpu_accelerated_preprocessing',
    'benchmark_optimization_performance',
    'simulate_original_processing',
    'configure_optimization_for_hardware',
    
    # From phase1_integration.py
    'impute_logs_deep_phase1_optimized',
    
    # From integration_preprocessing.py
    'integration_vectorized_preprocessing_pipeline',
    'cached_preprocessing_pipeline',
    'in_place_preprocessing_pipeline',
    'gpu_accelerated_preprocessing',
    'quick_data_quality_check',
    'impute_logs_deep_tensor_native',
    'fast_path_tensor_training',
    'clear_preprocessing_cache',
    'get_cache_info'
]