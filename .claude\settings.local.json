{"permissions": {"allow": ["<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(npx @modelcontextprotocol/server-sequential-thinking:*)", "Bash(npx:*)", "Bash(pip install:*)", "Bash(chcp:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "Bash(if exist __pycache__ rmdir /s /q __pycache__)", "<PERSON><PERSON>(rmdir:*)", "Bash(rm:*)", "Bash(ls:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(find:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(mkdir:*)", "Bash(move \"_adv_check.py\" \"archives\\debug_scripts\"\")", "Bash(move _adv_check.py archivesdebug_scripts)", "mcp__context7__resolve-library-id"], "deny": []}}