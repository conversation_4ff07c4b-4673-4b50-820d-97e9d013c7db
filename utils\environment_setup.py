"""
Environment Setup and Validation Module

This module handles environment setup, validation, and provides solutions
for common compilation and development environment issues on Windows.

Key Features:
- MSVC detection and setup
- Windows SDK validation
- CUDA environment configuration
- Development environment diagnostics

Author: ML Log Prediction System
Date: 2025-08-19
"""

import os
import sys
import warnings
import subprocess
from typing import Dict, List, Optional, Tuple
import platform

def suppress_compilation_warnings():
    """
    Suppress common compilation-related warnings that don't affect functionality.
    """
    # Suppress specific warnings
    warnings.filterwarnings('ignore', category=UserWarning, message='.*Failed to find MSVC.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*Failed to find Windows SDK.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*Failed to find CUDA.*')
    
    # Set environment variables to suppress build warnings
    os.environ['PYTHONWARNINGS'] = 'ignore::UserWarning'
    
    print("🔇 Compilation warnings suppressed for cleaner output")


def detect_msvc() -> Tuple[bool, Optional[str]]:
    """
    Detect Microsoft Visual C++ compiler availability.
    
    Returns:
        Tuple of (available, version_info)
    """
    try:
        # Try to find Visual Studio installations
        vs_paths = [
            r"C:\Program Files\Microsoft Visual Studio",
            r"C:\Program Files (x86)\Microsoft Visual Studio"
        ]
        
        for vs_path in vs_paths:
            if os.path.exists(vs_path):
                # Look for common VS versions
                for year in ['2022', '2019', '2017']:
                    full_path = os.path.join(vs_path, year)
                    if os.path.exists(full_path):
                        return True, f"Visual Studio {year} detected"
        
        # Try to run cl.exe (MSVC compiler)
        result = subprocess.run(['cl'], capture_output=True, text=True, shell=True)
        if result.returncode != 9009:  # 9009 means command not found
            return True, "MSVC compiler available"
            
    except Exception:
        pass
    
    return False, None


def detect_windows_sdk() -> Tuple[bool, Optional[str]]:
    """
    Detect Windows SDK availability.
    
    Returns:
        Tuple of (available, version_info)
    """
    try:
        # Common Windows SDK paths
        sdk_paths = [
            r"C:\Program Files (x86)\Windows Kits\10",
            r"C:\Program Files\Windows Kits\10"
        ]
        
        for sdk_path in sdk_paths:
            if os.path.exists(sdk_path):
                # Look for include directory
                include_path = os.path.join(sdk_path, "Include")
                if os.path.exists(include_path):
                    # Get latest version
                    versions = [d for d in os.listdir(include_path) 
                              if os.path.isdir(os.path.join(include_path, d)) and d.startswith('10.')]
                    if versions:
                        latest = sorted(versions)[-1]
                        return True, f"Windows SDK {latest}"
        
    except Exception:
        pass
    
    return False, None


def detect_cuda() -> Tuple[bool, Optional[str]]:
    """
    Detect CUDA toolkit availability.
    
    Returns:
        Tuple of (available, version_info)
    """
    try:
        # Check CUDA_PATH environment variable
        cuda_path = os.environ.get('CUDA_PATH')
        if cuda_path and os.path.exists(cuda_path):
            # Try to get CUDA version
            nvcc_path = os.path.join(cuda_path, 'bin', 'nvcc.exe')
            if os.path.exists(nvcc_path):
                result = subprocess.run([nvcc_path, '--version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    # Extract version from output
                    for line in result.stdout.split('\n'):
                        if 'release' in line.lower():
                            return True, f"CUDA toolkit detected: {line.strip()}"
                    return True, "CUDA toolkit detected"
        
        # Try to run nvcc directly
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            return True, "CUDA toolkit available in PATH"
            
    except Exception:
        pass
    
    return False, None


def run_environment_diagnostics() -> Dict[str, any]:
    """
    Run comprehensive environment diagnostics.
    
    Returns:
        Dictionary with diagnostic results
    """
    print("\n" + "="*50)
    print("🔍 ENVIRONMENT DIAGNOSTICS")
    print("="*50)
    
    diagnostics = {
        'platform': platform.platform(),
        'python_version': sys.version,
        'msvc_available': False,
        'windows_sdk_available': False,
        'cuda_available': False
    }
    
    # Check MSVC
    msvc_available, msvc_info = detect_msvc()
    diagnostics['msvc_available'] = msvc_available
    if msvc_available:
        print(f"✅ MSVC: {msvc_info}")
        diagnostics['msvc_info'] = msvc_info
    else:
        print("❌ MSVC: Not detected")
        print("   💡 Install Visual Studio with C++ tools for compilation support")
    
    # Check Windows SDK
    sdk_available, sdk_info = detect_windows_sdk()
    diagnostics['windows_sdk_available'] = sdk_available
    if sdk_available:
        print(f"✅ Windows SDK: {sdk_info}")
        diagnostics['sdk_info'] = sdk_info
    else:
        print("❌ Windows SDK: Not detected")
        print("   💡 Install Windows SDK for development headers")
    
    # Check CUDA
    cuda_available, cuda_info = detect_cuda()
    diagnostics['cuda_available'] = cuda_available
    if cuda_available:
        print(f"✅ CUDA: {cuda_info}")
        diagnostics['cuda_info'] = cuda_info
    else:
        print("❌ CUDA: Not detected")
        print("   💡 Install CUDA toolkit for GPU acceleration")
    
    print("\n📋 Summary:")
    if not any([msvc_available, sdk_available, cuda_available]):
        print("   ⚠️ No development tools detected - using pre-compiled packages only")
        print("   ✅ This is fine for most ML workflows using pip-installed packages")
    else:
        print("   ✅ Some development tools available")
    
    print("="*50)
    
    return diagnostics


def setup_development_environment():
    """
    Set up development environment with appropriate configurations.
    """
    # Suppress warnings first
    suppress_compilation_warnings()
    
    # Run diagnostics
    diagnostics = run_environment_diagnostics()
    
    # Configure environment based on available tools
    if not diagnostics['cuda_available']:
        # Force CPU-only mode for PyTorch and TensorFlow
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
        print("🔧 Configured for CPU-only execution")
    
    if not diagnostics['msvc_available']:
        # Set flags to avoid compilation attempts
        os.environ['DISTUTILS_USE_SDK'] = '1'
        print("🔧 Configured to use pre-compiled packages")
    
    return diagnostics


def provide_installation_guide():
    """
    Provide installation guide for missing development tools.
    """
    print("\n" + "="*60)
    print("📚 DEVELOPMENT TOOLS INSTALLATION GUIDE")
    print("="*60)
    
    print("\n🔨 Microsoft Visual C++ (MSVC):")
    print("   1. Install Visual Studio Community (free)")
    print("   2. Select 'Desktop development with C++' workload")
    print("   3. Or install 'Microsoft C++ Build Tools' standalone")
    
    print("\n🪟 Windows SDK:")
    print("   1. Download from Microsoft Developer website")
    print("   2. Or install via Visual Studio Installer")
    print("   3. Select latest Windows 10/11 SDK version")
    
    print("\n🚀 CUDA Toolkit (for GPU acceleration):")
    print("   1. Check GPU compatibility at developer.nvidia.com")
    print("   2. Download CUDA Toolkit matching your GPU")
    print("   3. Install cuDNN for deep learning frameworks")
    print("   4. Add CUDA to system PATH")
    
    print("\n💡 Quick Setup for ML Development:")
    print("   • For most users: Install Visual Studio Community")
    print("   • For GPU users: Add CUDA Toolkit")
    print("   • For CPU-only: Current setup is sufficient")
    
    print("="*60)


# Global flag to prevent duplicate initialization
_ENVIRONMENT_INITIALIZED = False

def initialize_environment_once():
    """
    Initialize environment only once, even if called multiple times.
    """
    global _ENVIRONMENT_INITIALIZED

    if _ENVIRONMENT_INITIALIZED:
        return None

    _ENVIRONMENT_INITIALIZED = True
    return setup_development_environment()


# Auto-setup on module import (only if not already initialized)
if __name__ != "__main__":
    # Only run setup if not being executed directly and not already initialized
    initialize_environment_once()
