import numpy as np
import pandas as pd
import torch
import os
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
# Import data handling functions - these are re-exported for backward compatibility
from .data_handler import (
    normalize_data,
    create_sequences,
    introduce_missingness,
    load_las_files_from_directory,
    clean_log_data,
    write_results_to_las
)
from torch.utils.data import DataLoader, TensorDataset
from tqdm import tqdm

# Import Multiple Linear Regression utilities
try:
    from preprocessing.mlr_preprocessing import MLRModelWrapper, create_mlr_model, validate_mlr_assumptions
    MLR_AVAILABLE = True
    print("Multiple Linear Regression utilities loaded successfully")
except ImportError as e:
    print(f"⚠️ Warning: MLR utilities not available: {e}")
    MLR_AVAILABLE = False
    MLRModelWrapper = None
    create_mlr_model = None
    validate_mlr_assumptions = None

# Import data leakage detection utilities
try:
    from .data_leakage_detector import comprehensive_leakage_check, detect_perfect_correlation_leakage
    DATA_LEAKAGE_DETECTION_AVAILABLE = True
    print("Data leakage detection module loaded")
except ImportError as e:
    print(f"⚠️ Data leakage detection not available: {e}")
    DATA_LEAKAGE_DETECTION_AVAILABLE = False
    comprehensive_leakage_check = None
    detect_perfect_correlation_leakage = None


def ensure_tensor_dtype_consistency(tensor):
    """
    Ensure tensor dtype consistency for autoencoder reconstruction.
    Converts torch.float16 to torch.float32 to prevent dtype mismatch errors.
    
    Args:
        tensor: Input tensor (typically model output)
        
    Returns:
        tensor: Tensor with consistent dtype (float32)
    """
    if tensor.dtype == torch.float16:
        return tensor.float()  # Convert to float32
    return tensor

# Import GPU utilities for gradient boosting optimization
try:
    from utils.gpu_acceleration import GPUManager
    from utils.optimization import GPUAccelerator
    from utils.xgboost_gpu_utils import get_xgboost_gpu_manager, create_optimized_xgboost_model
    GPU_UTILS_AVAILABLE = True
except ImportError:
    print("⚠️ GPU utilities not available for gradient boosting optimization")
    GPU_UTILS_AVAILABLE = False
    GPUManager = None
    GPUAccelerator = None
    get_xgboost_gpu_manager = lambda: None
    create_optimized_xgboost_model = None

# Autoencoder/UNet models removed from main pipeline
DEEP_MODELS_AVAILABLE = False
SimpleAutoencoder = None
SimpleUNet = None

# Import advanced deep learning models with safe fallbacks
try:
    from models.advanced_models import (
        SAITSModel, BRITSModel,
        ADVANCED_MODELS_AVAILABLE, ADVANCED_MODELS_STATUS, get_available_models as get_advanced_models,
        get_model_class, check_dependencies
    )
    print("Advanced deep learning models module loaded")
    if ADVANCED_MODELS_AVAILABLE:
        available_advanced = [k for k, v in ADVANCED_MODELS_STATUS.items() if v]
        print(f"Available advanced models: {available_advanced}")
    else:
        print("💡 Advanced models will be available after Phase 2 implementation")
except ImportError as e:
    print(f"⚠️ Advanced deep learning models not available: {e}")
    print("📝 This is expected during Phase 1 - continuing with basic models")
    ADVANCED_MODELS_AVAILABLE = False
    ADVANCED_MODELS_STATUS = {}
    SAITSModel = None
    BRITSModel = None
    get_advanced_models = lambda: {}
    get_model_class = lambda x: None
    check_dependencies = lambda: {'missing_dependencies': ['advanced_models']}

# --- PLUG-AND-PLAY MODEL REGISTRY ---
MODEL_REGISTRY = {
    'xgboost': {
        'name': 'XGBoost',
        'model_class': XGBRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 300, 'min': 50, 'max': 2000, 'prompt': "Number of estimators"},
            'learning_rate': {'type': float, 'default': 0.05, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'max_depth': {'type': int, 'default': 6, 'min': 3, 'max': 15, 'prompt': "Max tree depth"},
            'subsample': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': "Subsample ratio"},
            'colsample_bytree': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': "Column sample per tree"},
            'reg_alpha': {'type': float, 'default': 0, 'min': 0, 'max': 10, 'prompt': "L1 regularization"},
            'reg_lambda': {'type': float, 'default': 1, 'min': 0, 'max': 10, 'prompt': "L2 regularization"},
        },
        'fixed_params': {'random_state': 42},  # Removed early_stopping_rounds to avoid validation requirement
        'use_modern_gpu_config': True,  # Flag to use new XGBoost GPU utilities
        'gpu_check': {
            'param': 'device',  # Modern parameter name
            'gpu_value': 'cuda',  # Modern GPU value
            'cpu_value': 'cpu'   # Modern CPU value
        }
    },
    'lightgbm': {
        'name': 'LightGBM',
        'model_class': LGBMRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 300, 'min': 50, 'max': 2000, 'prompt': "Number of estimators"},
            'learning_rate': {'type': float, 'default': 0.1, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'max_depth': {'type': int, 'default': -1, 'min': -1, 'max': 15, 'prompt': "Max tree depth (-1 no limit)"},
            'min_child_samples': {'type': int, 'default': 20, 'min': 5, 'max': 100, 'prompt': "Min child samples"},
        },
        'fixed_params': {'random_state': 42},
        'gpu_check': {
            'param': 'device',
            'gpu_value': 'gpu',
            'cpu_value': 'cpu'
        }
    },
    'catboost': {
        'name': 'CatBoost',
        'model_class': CatBoostRegressor,
        'hyperparameters': {
            'iterations': {'type': int, 'default': 1000, 'min': 100, 'max': 5000, 'prompt': "Iterations"},
            'learning_rate': {'type': float, 'default': None, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'depth': {'type': int, 'default': 6, 'min': 3, 'max': 12, 'prompt': "Tree depth"},
            'l2_leaf_reg': {'type': float, 'default': 3, 'min': 1, 'max': 10, 'prompt': "L2 regularization"},
        },
        'fixed_params': {'random_state': 42, 'verbose': 0, 'early_stopping_rounds': 50},
        'gpu_check': {
            'param': 'task_type',
            'gpu_value': 'GPU',
            'cpu_value': 'CPU'
        }
    },
    
    
    # Multiple Linear Regression Models
    'linear_regression': {
        'name': 'Linear Regression',
        'model_class': lambda **kwargs: create_mlr_model('linear', **kwargs) if MLR_AVAILABLE else None,
        'type': 'shallow',
        'description': 'Standard multiple linear regression for well log imputation',
        'hyperparameters': {
            'fit_intercept': {'type': bool, 'default': True, 'prompt': 'Fit intercept'},
            'scaling_method': {'type': str, 'default': 'standard', 'options': ['standard', 'robust', 'minmax'], 'prompt': 'Feature scaling method'},
            'outlier_threshold': {'type': float, 'default': 3.0, 'min': 1.5, 'max': 5.0, 'prompt': 'Outlier Z-score threshold'},
            'vif_threshold': {'type': float, 'default': 10.0, 'min': 5.0, 'max': 20.0, 'prompt': 'VIF threshold for multicollinearity'},
            'handle_outliers': {'type': bool, 'default': True, 'prompt': 'Handle outliers automatically'},
            'enable_diagnostics': {'type': bool, 'default': False, 'prompt': 'Enable diagnostic output'}
        },
        'fixed_params': {'copy_X': True},
        'assumptions': ['linearity', 'independence', 'homoscedasticity', 'normality', 'no_multicollinearity'],
        'best_for': 'Interpretable baseline with linear relationships',
        'computational_cost': 'very_low'
    },
    'ridge_regression': {
        'name': 'Ridge Regression',
        'model_class': lambda **kwargs: create_mlr_model('ridge', **kwargs) if MLR_AVAILABLE else None,
        'type': 'shallow',
        'description': 'Ridge regression with L2 regularization for handling multicollinearity',
        'hyperparameters': {
            'alpha': {'type': float, 'default': 1.0, 'min': 0.001, 'max': 100.0, 'prompt': 'L2 regularization strength'},
            'fit_intercept': {'type': bool, 'default': True, 'prompt': 'Fit intercept'},
            'solver': {'type': str, 'default': 'auto', 'options': ['auto', 'svd', 'cholesky', 'lsqr', 'sparse_cg', 'sag', 'saga'], 'prompt': 'Solver algorithm'},
            'scaling_method': {'type': str, 'default': 'standard', 'options': ['standard', 'robust', 'minmax'], 'prompt': 'Feature scaling method'},
            'outlier_threshold': {'type': float, 'default': 3.0, 'min': 1.5, 'max': 5.0, 'prompt': 'Outlier Z-score threshold'},
            'vif_threshold': {'type': float, 'default': 10.0, 'min': 5.0, 'max': 20.0, 'prompt': 'VIF threshold for multicollinearity'},
            'handle_outliers': {'type': bool, 'default': True, 'prompt': 'Handle outliers automatically'},
            'enable_diagnostics': {'type': bool, 'default': False, 'prompt': 'Enable diagnostic output'}
        },
        'fixed_params': {},
        'assumptions': ['linearity', 'independence', 'homoscedasticity'],
        'best_for': 'Handling multicollinearity with L2 regularization',
        'computational_cost': 'very_low'
    },
    'lasso_regression': {
        'name': 'Lasso Regression',
        'model_class': lambda **kwargs: create_mlr_model('lasso', **kwargs) if MLR_AVAILABLE else None,
        'type': 'shallow',
        'description': 'Lasso regression with L1 regularization for feature selection',
        'hyperparameters': {
            'alpha': {'type': float, 'default': 1.0, 'min': 0.001, 'max': 100.0, 'prompt': 'L1 regularization strength'},
            'fit_intercept': {'type': bool, 'default': True, 'prompt': 'Fit intercept'},
            'max_iter': {'type': int, 'default': 1000, 'min': 100, 'max': 10000, 'prompt': 'Maximum iterations'},
            'selection': {'type': str, 'default': 'cyclic', 'options': ['cyclic', 'random'], 'prompt': 'Feature selection method'},
            'scaling_method': {'type': str, 'default': 'standard', 'options': ['standard', 'robust', 'minmax'], 'prompt': 'Feature scaling method'},
            'outlier_threshold': {'type': float, 'default': 3.0, 'min': 1.5, 'max': 5.0, 'prompt': 'Outlier Z-score threshold'},
            'vif_threshold': {'type': float, 'default': 10.0, 'min': 5.0, 'max': 20.0, 'prompt': 'VIF threshold for multicollinearity'},
            'handle_outliers': {'type': bool, 'default': True, 'prompt': 'Handle outliers automatically'},
            'enable_diagnostics': {'type': bool, 'default': False, 'prompt': 'Enable diagnostic output'}
        },
        'fixed_params': {},
        'assumptions': ['linearity', 'independence', 'homoscedasticity'],
        'best_for': 'Automatic feature selection with L1 regularization',
        'computational_cost': 'very_low'
    },
    'elastic_net': {
        'name': 'ElasticNet Regression',
        'model_class': lambda **kwargs: create_mlr_model('elastic_net', **kwargs) if MLR_AVAILABLE else None,
        'type': 'shallow',
        'description': 'ElasticNet regression combining L1 and L2 regularization',
        'hyperparameters': {
            'alpha': {'type': float, 'default': 1.0, 'min': 0.001, 'max': 100.0, 'prompt': 'Overall regularization strength'},
            'l1_ratio': {'type': float, 'default': 0.5, 'min': 0.0, 'max': 1.0, 'prompt': 'L1 vs L2 ratio (0=Ridge, 1=Lasso)'},
            'fit_intercept': {'type': bool, 'default': True, 'prompt': 'Fit intercept'},
            'max_iter': {'type': int, 'default': 1000, 'min': 100, 'max': 10000, 'prompt': 'Maximum iterations'},
            'selection': {'type': str, 'default': 'cyclic', 'options': ['cyclic', 'random'], 'prompt': 'Feature selection method'},
            'scaling_method': {'type': str, 'default': 'standard', 'options': ['standard', 'robust', 'minmax'], 'prompt': 'Feature scaling method'},
            'outlier_threshold': {'type': float, 'default': 3.0, 'min': 1.5, 'max': 5.0, 'prompt': 'Outlier Z-score threshold'},
            'vif_threshold': {'type': float, 'default': 10.0, 'min': 5.0, 'max': 20.0, 'prompt': 'VIF threshold for multicollinearity'},
            'handle_outliers': {'type': bool, 'default': True, 'prompt': 'Handle outliers automatically'},
            'enable_diagnostics': {'type': bool, 'default': False, 'prompt': 'Enable diagnostic output'}
        },
        'fixed_params': {},
        'assumptions': ['linearity', 'independence', 'homoscedasticity'],
        'best_for': 'Balanced feature selection and multicollinearity handling',
        'computational_cost': 'very_low'
    }
}

# Enhanced model registry with advanced models (Phase 2 implementation)
if ADVANCED_MODELS_AVAILABLE:
    # Build advanced model configurations dynamically based on available models
    ADVANCED_MODEL_CONFIGS = {}

    # SAITS Model Configuration
    if ADVANCED_MODELS_STATUS.get('saits', False) and SAITSModel is not None:
        ADVANCED_MODEL_CONFIGS['saits'] = {
            'name': 'SAITS (Self-Attention)',
            'model_class': SAITSModel,
            'type': 'deep_advanced',
            'description': 'State-of-the-art self-attention model for time series imputation',
            'performance_tier': 'highest',
            'computational_cost': 'high',
            'memory_intensive': True,
            'requires_gpu': False,  # Optional but recommended
            'gpu_accelerated': True,  # Supports GPU acceleration
            'mixed_precision_support': True,  # Supports automatic mixed precision
            'hyperparameters': {
                'sequence_len': {'type': int, 'default': 64, 'min': 32, 'max': 128, 'prompt': "Sequence length"},
                'n_features': {'type': int, 'default': 4, 'prompt': "Number of features"},
                'n_layers': {'type': int, 'default': 2, 'min': 1, 'max': 6, 'prompt': "Transformer layers"},
                'd_model': {'type': int, 'default': 256, 'min': 64, 'max': 512, 'prompt': "Model dimension"},
                'n_heads': {'type': int, 'default': 4, 'min': 2, 'max': 16, 'prompt': "Attention heads"},
                'epochs': {'type': int, 'default': 40, 'min': 10, 'max': 200, 'prompt': "Training epochs"},  # Reduced from 50
                'batch_size': {'type': int, 'default': 64, 'min': 8, 'max': 256, 'prompt': "Batch size"},  # Increased from 32
                'learning_rate': {'type': float, 'default': 2e-3, 'min': 1e-5, 'max': 1e-2, 'prompt': "Learning rate"},  # Increased from 1e-3
                'early_stopping_patience': {'type': int, 'default': 12},  # Added early stopping
                'dropout': {'type': float, 'default': 0.1, 'min': 0.0, 'max': 0.5, 'prompt': "Dropout rate"},
                'device': {'type': str, 'default': None, 'options': ['cuda', 'cpu', None], 'prompt': "Device (cuda/cpu/auto)"},
                'use_mixed_precision': {'type': bool, 'default': True, 'prompt': "Use mixed precision training"},
            },
            'fixed_params': {},
            'recommended_for': ['complex_patterns', 'long_sequences', 'high_accuracy'],
            'best_for': 'Maximum accuracy with complex temporal patterns',
            'training_time': 'High (2-5x longer than basic models)',
            'model_size': 'Large (>1M parameters)'
        }
        print("SAITS model added to registry")

    # BRITS Model Configuration
    if ADVANCED_MODELS_STATUS.get('brits', False) and BRITSModel is not None:
        ADVANCED_MODEL_CONFIGS['brits'] = {
            'name': 'BRITS (Bidirectional RNN)',
            'model_class': BRITSModel,
            'type': 'deep_advanced',
            'description': 'Bidirectional RNN specialized for temporal dependencies',
            'performance_tier': 'high',
            'computational_cost': 'medium',
            'memory_intensive': False,
            'requires_gpu': False,  # Optional but recommended
            'gpu_accelerated': True,  # Supports GPU acceleration
            'mixed_precision_support': True,  # Supports automatic mixed precision
            'hyperparameters': {
                'sequence_len': {'type': int, 'default': 64, 'min': 16, 'max': 128, 'prompt': "Sequence length"},
                'n_features': {'type': int, 'default': 4, 'prompt': "Number of features"},
                'rnn_hidden_size': {'type': int, 'default': 128, 'min': 32, 'max': 512, 'prompt': "RNN hidden size"},
                'epochs': {'type': int, 'default': 40, 'min': 10, 'max': 200, 'prompt': "Training epochs"},  # Reduced from 50
                'batch_size': {'type': int, 'default': 256, 'min': 8, 'max': 512, 'prompt': "Batch size"},  # Increased from 128
                'early_stopping_patience': {'type': int, 'default': 12},  # Added early stopping
                'learning_rate': {'type': float, 'default': 1e-3, 'min': 1e-5, 'max': 1e-2, 'prompt': "Learning rate"},
                'device': {'type': str, 'default': None, 'options': ['cuda', 'cpu', None], 'prompt': "Device (cuda/cpu/auto)"},
                'use_mixed_precision': {'type': bool, 'default': True, 'prompt': "Use mixed precision training"},
                'num_workers': {'type': int, 'default': 4, 'min': 0, 'max': 16, 'prompt': "Dataloader workers"},
                # Note: gradient_accumulation_steps removed - not supported by PyPOTS BRITS model
            },
            'fixed_params': {},
            'recommended_for': ['temporal_patterns', 'sequential_data', 'medium_complexity'],
            'best_for': 'Good balance of accuracy and training speed',
            'training_time': 'Medium (1.5-2x longer than basic models)',
            'model_size': 'Medium (100K-1M parameters)'
        }
        print("BRITS model added to registry")

    # Enhanced U-Net (placeholder for Phase 3)
    # Enhanced U-Net removed from registry

    # Transformer model has been removed



    # mRNN model has been removed

    # Safely add advanced models to registry
    if ADVANCED_MODEL_CONFIGS:
        MODEL_REGISTRY.update(ADVANCED_MODEL_CONFIGS)
        print(f"Enhanced MODEL_REGISTRY with {len(ADVANCED_MODEL_CONFIGS)} advanced models")
        print(f"Available advanced models: {list(ADVANCED_MODEL_CONFIGS.keys())}")
    else:
        print("⚠️ No advanced models available - check Phase 2 implementation")
else:
    print("📝 Advanced models not available - using basic models only")

# Enhanced model management functions
def check_model_dependencies():
    """Check and report model dependencies status."""
    status_report = {
        'basic_models': DEEP_MODELS_AVAILABLE,
        'advanced_models': ADVANCED_MODELS_AVAILABLE,
        'individual_models': ADVANCED_MODELS_STATUS if ADVANCED_MODELS_AVAILABLE else {},
        'recommendations': []
    }

    if not DEEP_MODELS_AVAILABLE:
        status_report['recommendations'].append("Install PyTorch for basic deep learning models")

    if not ADVANCED_MODELS_AVAILABLE:
        status_report['recommendations'].append("Complete Phase 2 implementation for advanced models")
        status_report['recommendations'].append("Ensure PyPOTS and MONAI are installed")

    return status_report

def get_model_class_safe(model_key: str):
    """
    Safely get model class with fallback handling.

    Args:
        model_key: Model identifier

    Returns:
        Model class or None if not available
    """
    if model_key in MODEL_REGISTRY:
        model_class = MODEL_REGISTRY[model_key]['model_class']
        if model_class is not None:
            return model_class
        else:
            print(f"⚠️ Model {model_key} not available - missing dependencies or implementation")
            return None
    else:
        print(f"❌ Unknown model key: {model_key}")
        return None

def get_available_models_by_type():
    """
    Get list of available models organized by type.

    Returns:
        dict: Available models organized by type
    """
    available = {
        'shallow': [],
        'deep_basic': [],
        'deep_advanced': []
    }

    for model_key, config in MODEL_REGISTRY.items():
        model_type = config.get('type', 'shallow')

        # Map 'deep' to 'deep_basic' for backward compatibility
        if model_type == 'deep':
            model_type = 'deep_basic'

        model_class = config.get('model_class')

        if model_class is not None:
            model_info = {
                'key': model_key,
                'name': config['name'],
                'description': config.get('description', 'No description available'),
                'performance_tier': config.get('performance_tier', 'standard'),
                'computational_cost': config.get('computational_cost', 'low'),
                'available': True
            }

            if model_type == 'deep_advanced':
                model_info.update({
                    'memory_intensive': config.get('memory_intensive', False),
                    'requires_gpu': config.get('requires_gpu', False),
                    'best_for': config.get('best_for', 'General purpose'),
                    'training_time': config.get('training_time', 'Standard'),
                    'model_size': config.get('model_size', 'Standard')
                })

            available[model_type].append(model_info)
        else:
            # Add unavailable model info
            available[model_type].append({
                'key': model_key,
                'name': config['name'],
                'description': config.get('description', 'No description available'),
                'available': False,
                'reason': 'Dependencies not available'
            })

    return available

def get_models_by_performance_tier(tier: str = 'all') -> list:
    """
    Get models filtered by performance tier.

    Args:
        tier: 'highest', 'high', 'medium', 'standard', or 'all'

    Returns:
        List of model keys matching the tier
    """
    if tier == 'all':
        return [k for k, v in MODEL_REGISTRY.items() if v.get('model_class') is not None]

    matching_models = []
    for model_key, config in MODEL_REGISTRY.items():
        if (config.get('performance_tier', 'standard') == tier and
            config.get('model_class') is not None):
            matching_models.append(model_key)

    return matching_models

def get_computational_cost_estimate(model_key: str) -> str:
    """Get computational cost estimate for a model."""
    if model_key in MODEL_REGISTRY:
        return MODEL_REGISTRY[model_key].get('computational_cost', 'unknown')
    return 'unknown'

def recommend_models_for_task(task_requirements: dict) -> list:
    """
    Recommend models based on task requirements.

    Args:
        task_requirements: dict with keys like 'accuracy_priority', 'speed_priority', 'data_complexity'

    Returns:
        list: Recommended model keys in priority order
    """
    recommendations = []

    # High accuracy priority
    if task_requirements.get('accuracy_priority', False):
        if ADVANCED_MODELS_AVAILABLE:
            recommendations.extend(['saits', 'brits'])

    # Speed priority
    elif task_requirements.get('speed_priority', False):
        recommendations.extend(['xgboost', 'lightgbm', 'catboost'])

    # Balanced approach (default)
    else:
        if ADVANCED_MODELS_AVAILABLE:
            recommendations.extend(['brits'])
        recommendations.extend(['xgboost', 'lightgbm'])

    # Filter out unavailable models and remove duplicates
    available_recommendations = []
    seen = set()
    for model in recommendations:
        if (model not in seen and
            model in MODEL_REGISTRY and
            MODEL_REGISTRY[model].get('model_class') is not None):
            seen.add(model)
            available_recommendations.append(model)

    return available_recommendations

def create_model_performance_summary() -> dict:
    """
    Create a comprehensive summary of all available models and their characteristics.

    Returns:
        dict: Comprehensive model summary with performance tiers, costs, and recommendations
    """
    summary = {
        'total_models': len(MODEL_REGISTRY),
        'available_models': 0,
        'by_type': {
            'shallow': {'count': 0, 'models': []},
            'deep_basic': {'count': 0, 'models': []},
            'deep_advanced': {'count': 0, 'models': []}
        },
        'by_performance_tier': {
            'highest': [],
            'high': [],
            'medium': [],
            'standard': []
        },
        'by_computational_cost': {
            'low': [],
            'medium': [],
            'high': []
        },
        'recommendations': {
            'for_accuracy': [],
            'for_speed': [],
            'for_balance': []
        }
    }

    # Analyze each model
    for model_key, config in MODEL_REGISTRY.items():
        model_class = config.get('model_class')
        if model_class is not None:
            summary['available_models'] += 1

            # Categorize by type
            model_type = config.get('type', 'shallow')
            if model_type in summary['by_type']:
                summary['by_type'][model_type]['count'] += 1
                summary['by_type'][model_type]['models'].append({
                    'key': model_key,
                    'name': config['name'],
                    'description': config.get('description', ''),
                    'computational_cost': config.get('computational_cost', 'low'),
                    'performance_tier': config.get('performance_tier', 'standard')
                })

            # Categorize by performance tier
            perf_tier = config.get('performance_tier', 'standard')
            if perf_tier in summary['by_performance_tier']:
                summary['by_performance_tier'][perf_tier].append(model_key)

            # Categorize by computational cost
            comp_cost = config.get('computational_cost', 'low')
            if comp_cost in summary['by_computational_cost']:
                summary['by_computational_cost'][comp_cost].append(model_key)

    # Generate recommendations
    summary['recommendations']['for_accuracy'] = recommend_models_for_task({'accuracy_priority': True})
    summary['recommendations']['for_speed'] = recommend_models_for_task({'speed_priority': True})
    summary['recommendations']['for_balance'] = recommend_models_for_task({'balanced': True})

    return summary

def print_model_registry_status():
    """Print a comprehensive status report of the model registry."""
    print("\n" + "="*60)
    print("MODEL REGISTRY STATUS REPORT")
    print("="*60)

    summary = create_model_performance_summary()

    print(f"Total Models: {summary['total_models']}")
    print(f"Available Models: {summary['available_models']}")
    print(f"Unavailable Models: {summary['total_models'] - summary['available_models']}")

    print("\nModels by Type:")
    for model_type, info in summary['by_type'].items():
        print(f"  {model_type.replace('_', ' ').title()}: {info['count']} models")
        for model in info['models']:
            status = "Available" if model else "Unavailable"
            print(f"    - {model['name']} ({model['key']}): {status}")

    print("\nPerformance Tiers:")
    for tier, models in summary['by_performance_tier'].items():
        if models:
            print(f"  {tier.title()}: {', '.join(models)}")

    print("\nComputational Cost:")
    for cost, models in summary['by_computational_cost'].items():
        if models:
            print(f"  {cost.title()}: {', '.join(models)}")

    print("\nRecommendations:")
    print(f"  For Accuracy: {', '.join(summary['recommendations']['for_accuracy'][:3])}")
    print(f"  For Speed: {', '.join(summary['recommendations']['for_speed'][:3])}")
    print(f"  For Balance: {', '.join(summary['recommendations']['for_balance'][:3])}")

    print("="*60)

def recommend_models_for_task_enhanced(task_requirements: dict = None) -> list:
    """
    Recommend models based on task requirements with availability checking.

    Args:
        task_requirements: dict with keys like 'accuracy_priority', 'speed_priority', 'data_complexity'

    Returns:
        list: Recommended model keys in priority order (only available models)
    """
    if task_requirements is None:
        task_requirements = {'balanced': True}

    all_recommendations = []

    # High accuracy priority
    if task_requirements.get('accuracy_priority', False):
        if ADVANCED_MODELS_AVAILABLE:
            if ADVANCED_MODELS_STATUS.get('saits', False):
                all_recommendations.append('saits')
            if ADVANCED_MODELS_STATUS.get('brits', False):
                all_recommendations.append('brits')


    # Speed priority
    if task_requirements.get('speed_priority', False):
        pass

    # Balanced approach (default)
    if task_requirements.get('balanced', True):
        if ADVANCED_MODELS_AVAILABLE:
            if ADVANCED_MODELS_STATUS.get('brits', False):
                all_recommendations.append('brits')

    # Remove duplicates while preserving order and filter available models
    seen = set()
    available_recommendations = []
    for model in all_recommendations:
        if model not in seen and model in MODEL_REGISTRY:
            model_class = MODEL_REGISTRY[model]['model_class']
            if model_class is not None:  # Only include available models
                seen.add(model)
                available_recommendations.append(model)

    return available_recommendations

def get_model_config(model_name: str) -> dict:
    """
    Enhanced model configuration retrieval supporting both transformer modes.

    Args:
        model_name: Model identifier from MODEL_REGISTRY

    Returns:
        Model configuration dictionary

    Raises:
        ValueError: If model_name is not found in registry
    """
    if model_name not in MODEL_REGISTRY:
        available_models = list(MODEL_REGISTRY.keys())
        raise ValueError(f"Model '{model_name}' not found in registry. Available models: {available_models}")

    config = MODEL_REGISTRY[model_name].copy()

    return config

def get_compatible_models(requirements: dict = None) -> list:
    """
    Get models compatible with specific requirements.

    Args:
        requirements: Dictionary with compatibility requirements
                     e.g., {'mode': 'prediction_only', 'memory_efficient': True}

    Returns:
        List of compatible model keys
    """
    if requirements is None:
        requirements = {}

    compatible_models = []

    for model_key, config in MODEL_REGISTRY.items():
        model_class = config.get('model_class')
        if model_class is None:
            continue

        # Check memory efficiency requirements
        if requirements.get('memory_efficient', False):
            if config.get('memory_intensive', False):
                continue

        # Check GPU requirements
        if requirements.get('gpu_required', False):
            if not config.get('requires_gpu', False):
                continue
        elif requirements.get('cpu_only', False):
            if config.get('requires_gpu', False):
                continue

        compatible_models.append(model_key)

    return compatible_models

def create_enhanced_model_instance(model_config: dict, hyperparams: dict):
    """
    Create enhanced model instance with automatic mode detection and configuration.

    Args:
        model_config: Model configuration from MODEL_REGISTRY
        hyperparams: Hyperparameters dictionary

    Returns:
        Configured model instance
    """
    model_class = model_config['model_class']
    model_name = model_config.get('name', 'Unknown')

    # Apply fixed parameters from configuration
    final_params = hyperparams.copy()
    if 'fixed_params' in model_config:
        final_params.update(model_config['fixed_params'])
        print(f"🔧 Applied fixed parameters for {model_name}: {model_config['fixed_params']}")

    # Enhanced configuration for transformer models
    if 'transformer' in model_name.lower():
        mode = model_config.get('mode', 'imputation_based')

        if mode == 'prediction_only':
            print(f"🚀 Creating {model_name} in prediction-only mode with optimized hyperparameters")
            # Ensure prediction-only mode is enabled
            final_params['use_prediction_only'] = True

            # Log the enhanced configuration
            lr = final_params.get('learning_rate', 1e-3)
            clip_norm = final_params.get('gradient_clip_norm', 1.0)
            print(f"   • Learning Rate: {lr} (standard for stable training)")
            print(f"   • Gradient Clipping: {clip_norm} (increased for stability)")
            print(f"   • Memory Optimization: Enabled (40-50% reduction expected)")
            print(f"   • Missing Value Processing: Eliminated from computation graph")

        else:
            print(f"📊 Creating {model_name} in imputation-based mode with conservative hyperparameters")
            # Ensure imputation mode (default behavior)
            final_params['use_prediction_only'] = False

            # Log the conservative configuration
            lr = final_params.get('learning_rate', 1e-5)
            clip_norm = final_params.get('gradient_clip_norm', 0.5)
            print(f"   • Learning Rate: {lr} (conservative for stability)")
            print(f"   • Gradient Clipping: {clip_norm} (aggressive for gradient stability)")
            print(f"   • Missing Value Processing: Included in computation graph")

    # Create model instance
    try:
        model = model_class(**final_params)
        print(f"✅ {model_name} model created successfully")

        # Add model metadata for tracking
        if hasattr(model, '__dict__'):
            model._model_config = model_config
            model._creation_params = final_params

        return model

    except Exception as e:
        print(f"❌ Failed to create {model_name} model: {e}")
        print(f"   Parameters: {final_params}")
        raise

def create_gpu_optimized_model(model_key, hyperparams):
    """
    Create a GPU-optimized model instance with automatic GPU parameter detection.

    Args:
        model_key: Model identifier from MODEL_REGISTRY
        hyperparams: Hyperparameters dictionary

    Returns:
        Optimized model instance
    """
    if model_key not in MODEL_REGISTRY:
        raise ValueError(f"Unknown model key: {model_key}")

    model_config = MODEL_REGISTRY[model_key]
    model_class = model_config['model_class']

    if model_class is None:
        raise ValueError(f"Model {model_key} not available")

    # Create base hyperparameters
    final_params = hyperparams.copy()

    # Add fixed parameters
    if 'fixed_params' in model_config:
        final_params.update(model_config['fixed_params'])

    # Handle XGBoost with modern GPU configuration
    if model_key == 'xgboost' and model_config.get('use_modern_gpu_config') and GPU_UTILS_AVAILABLE:
        # Use the new XGBoost GPU utilities
        try:
            model = create_optimized_xgboost_model(model_class, final_params)
            print(f"{model_config['name']} model created with optimized GPU/CPU configuration")
            return model
        except Exception as e:
            print(f"❌ Failed to create optimized XGBoost model: {e}")
            # Fall through to legacy configuration

    # Legacy GPU optimization for other models or XGBoost fallback
    gpu_config = model_config.get('gpu_check')
    if gpu_config and torch.cuda.is_available():
        param_name = gpu_config['param']
        gpu_value = gpu_config['gpu_value']

        # Enable GPU parameter
        final_params[param_name] = gpu_value
        print(f"{model_config['name']}: GPU acceleration enabled ({param_name}={gpu_value})")

        # Additional GPU optimizations for non-XGBoost models
        if model_key == 'lightgbm':
            # LightGBM GPU optimizations
            final_params['gpu_platform_id'] = 0
            final_params['gpu_device_id'] = 0
            print(f"   • LightGBM GPU platform/device configured")

        elif model_key == 'catboost':
            # CatBoost GPU optimizations
            final_params['devices'] = '0'
            print(f"   • CatBoost GPU device configured")

        elif model_key == 'xgboost':
            # Legacy XGBoost configuration (fallback)
            final_params['tree_method'] = 'hist'  # Use modern tree_method
            print(f"   • XGBoost using legacy configuration with modern tree_method")
    else:
        # Use CPU parameters
        if gpu_config:
            param_name = gpu_config['param']
            cpu_value = gpu_config['cpu_value']
            final_params[param_name] = cpu_value
            print(f"{model_config['name']}: Using CPU ({param_name}={cpu_value})")

            # Ensure XGBoost uses modern tree_method even for CPU
            if model_key == 'xgboost':
                final_params['tree_method'] = 'hist'

    # Create and return the model
    try:
        model = model_class(**final_params)
        print(f"{model_config['name']} model created successfully")
        return model
    except Exception as e:
        print(f"❌ Failed to create {model_config['name']} model: {e}")

        # Enhanced fallback logic with complete GPU parameter removal
        if gpu_config:
            print(f"   🔄 Attempting comprehensive CPU fallback...")

            # Start with base hyperparameters
            fallback_params = hyperparams.copy()
            fallback_params.update(model_config.get('fixed_params', {}))

            # Set CPU configuration
            fallback_params[gpu_config['param']] = gpu_config['cpu_value']

            # Remove ALL GPU-related parameters for each model type
            if model_key == 'xgboost':
                gpu_params_to_remove = [
                    'gpu_id', 'predictor', 'gpu_predictor',
                    'gpu_platform_id', 'gpu_device_id'
                ]
                for param in gpu_params_to_remove:
                    fallback_params.pop(param, None)

                # Ensure modern CPU configuration
                fallback_params['tree_method'] = 'hist'
                fallback_params['device'] = 'cpu'

            elif model_key == 'lightgbm':
                gpu_params_to_remove = ['gpu_platform_id', 'gpu_device_id']
                for param in gpu_params_to_remove:
                    fallback_params.pop(param, None)
                fallback_params['device'] = 'cpu'

            elif model_key == 'catboost':
                gpu_params_to_remove = ['devices']
                for param in gpu_params_to_remove:
                    fallback_params.pop(param, None)
                fallback_params['task_type'] = 'CPU'

            try:
                model = model_class(**fallback_params)
                print(f"{model_config['name']} model created with comprehensive CPU fallback")
                return model
            except Exception as fallback_e:
                print(f"❌ Comprehensive CPU fallback also failed: {fallback_e}")

                # Last resort: minimal configuration
                print(f"   🔄 Attempting minimal configuration fallback...")
                minimal_params = {
                    'random_state': 42,
                    'n_estimators': hyperparams.get('n_estimators', 100)
                }

                # Add model-specific minimal CPU parameters
                if model_key == 'xgboost':
                    minimal_params.update({
                        'tree_method': 'hist',
                        'device': 'cpu'
                    })
                elif model_key == 'lightgbm':
                    minimal_params['device'] = 'cpu'
                elif model_key == 'catboost':
                    minimal_params.update({
                        'task_type': 'CPU',
                        'verbose': 0
                    })

                try:
                    model = model_class(**minimal_params)
                    print(f"{model_config['name']} model created with minimal fallback configuration")
                    return model
                except Exception as minimal_e:
                    print(f"❌ Even minimal configuration failed: {minimal_e}")
                    raise minimal_e
        else:
            raise

def evaluate_model_comprehensive(model, X_train, y_train, X_val, y_val):
    """Return MAE, RMSE, R2 and a composite score."""
    y_pred = model.predict(X_val)
    mae = mean_absolute_error(y_val, y_pred)
    rmse = np.sqrt(mean_squared_error(y_val, y_pred))
    r2 = r2_score(y_val, y_pred)

    # Handle cases where R² is negative (poor model performance)
    r2_penalty = (1 - r2) if r2 > 0 else (1 + abs(r2))

    # Weighted composite score
    composite = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)


    return {'mae': mae, 'rmse': rmse, 'r2': r2, 'composite_score': composite}


def evaluate_prediction_only_model(
    model,
    val_sequences_missing,
    val_sequences_true,
    test_df=None,
    feature_cols=None,
    target_col=None,
    all_features=None,
    scalers=None,
    sequence_len=64,
    use_enhanced_preprocessing=True,
    **kwargs
):
    """
    Evaluate prediction-only transformer model with appropriate metrics.

    Args:
        model: Trained prediction-only transformer model
        val_sequences_missing: Validation sequences with missing values
        val_sequences_true: Original validation sequences
        test_df: Optional test dataframe for additional evaluation
        feature_cols: List of feature column names
        target_col: Target column name
        all_features: List of all features including target
        scalers: Dictionary of scalers for inverse transformation
        sequence_len: Length of sequences
        use_enhanced_preprocessing: Whether to use enhanced preprocessing
        **kwargs: Additional parameters

    Returns:
        dict: Evaluation metrics optimized for prediction-only mode
    """
    print("🚀 Evaluating Prediction-Only Transformer...")

    results = {
        'prediction_metrics': {},
        'performance_analysis': {},
        'mode': 'prediction_only'
    }

    # Validate inputs
    if val_sequences_missing is None or val_sequences_true is None:
        print("⚠️ Warning: Missing validation sequences for evaluation")
        return results

    if all_features is None or target_col is None:
        print("⚠️ Warning: Missing feature configuration for evaluation")
        return results

    target_idx = all_features.index(target_col)

    # ========== PREDICTION-ONLY EVALUATION ==========
    print("Evaluating prediction performance on valid data points...")

    # Use the model's prediction-only capabilities
    with torch.no_grad():
        if hasattr(model, 'predict'):
            predictions = model.predict(val_sequences_missing)
        else:
            model.eval()
            predictions = model(val_sequences_missing)

    # Extract valid (non-missing) data points for evaluation
    valid_mask = ~torch.isnan(val_sequences_true[:, :, target_idx])

    if valid_mask.sum() > 0:
        # Get predictions and targets for valid points only
        y_pred = predictions[:, :, target_idx][valid_mask].cpu().numpy()
        y_true = val_sequences_true[:, :, target_idx][valid_mask].cpu().numpy()

        # Calculate metrics
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))

        # Calculate composite score
        r2_penalty = (1 - r2) if r2 > 0 else (1 + abs(r2))
        composite_score = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)

        results['prediction_metrics'] = {
            'mae': mae,
            'r2': r2,
            'rmse': rmse,
            'composite_score': composite_score,
            'n_samples': len(y_true),
            'task_description': 'Prediction on valid data points only'
        }

        print(f"Prediction-Only Metrics:")
        print(f"   • MAE: {mae:.4f}")
        print(f"   • R²: {r2:.4f}")
        print(f"   • RMSE: {rmse:.4f}")
        print(f"   • Composite Score: {composite_score:.4f}")
        print(f"   • Valid Samples: {len(y_true)}")

        # Performance analysis
        results['performance_analysis'] = {
            'memory_efficiency': 'Optimized (40-50% reduction expected)',
            'gradient_stability': 'Enhanced (no missing value tokens)',
            'training_speed': 'Improved (5-10x faster expected)',
            'hyperparameter_stability': 'Standard (LR=1e-3, Clip=1.0)'
        }

    else:
        print("⚠️ Warning: No valid data points found for evaluation")
        results['prediction_metrics'] = {
            'mae': float('inf'),
            'r2': -float('inf'),
            'rmse': float('inf'),
            'composite_score': float('inf'),
            'n_samples': 0,
            'task_description': 'No valid data available'
        }

    return results


def evaluate_imputation_and_prediction(
    model,
    val_sequences_missing,
    val_sequences_true,
    test_df,
    feature_cols,
    target_col,
    all_features,
    scalers,
    sequence_len=64,
    use_enhanced_preprocessing=True,
    **kwargs
):
    """
    Enhanced evaluation function with automatic mode detection for both transformer modes.

    This function automatically detects the transformer mode and applies appropriate evaluation:
    1. For prediction-only mode: Evaluates performance on valid data points only
    2. For imputation mode: Evaluates both imputation and prediction tasks
    3. Provides mode-specific metrics and analysis

    Args:
        model: Trained deep learning model (Transformer, SAITS, BRITS, etc.)
        val_sequences_missing: Validation sequences with artificial missing values
        val_sequences_true: Original validation sequences
        test_df: Held-out test dataframe for prediction evaluation
        feature_cols: List of feature column names
        target_col: Target column name
        all_features: List of all features including target
        scalers: Dictionary of scalers for inverse transformation
        sequence_len: Length of sequences
        use_enhanced_preprocessing: Whether to use enhanced preprocessing
        **kwargs: Additional parameters

    Returns:
        dict: Comprehensive evaluation metrics appropriate for the model mode
    """

    # ========== AUTOMATIC MODE DETECTION ==========
    is_prediction_only = hasattr(model, 'prediction_only_mode') and model.prediction_only_mode

    if is_prediction_only:
        print("🚀 Detected Prediction-Only Transformer - Using optimized evaluation")
        return evaluate_prediction_only_model(
            model=model,
            val_sequences_missing=val_sequences_missing,
            val_sequences_true=val_sequences_true,
            test_df=test_df,
            feature_cols=feature_cols,
            target_col=target_col,
            all_features=all_features,
            scalers=scalers,
            sequence_len=sequence_len,
            use_enhanced_preprocessing=use_enhanced_preprocessing,
            **kwargs
        )
    else:
        print("📊 Detected Imputation-Based Model - Using comprehensive evaluation")
        return evaluate_imputation_model(
            model=model,
            val_sequences_missing=val_sequences_missing,
            val_sequences_true=val_sequences_true,
            test_df=test_df,
            feature_cols=feature_cols,
            target_col=target_col,
            all_features=all_features,
            scalers=scalers,
            sequence_len=sequence_len,
            use_enhanced_preprocessing=use_enhanced_preprocessing,
            **kwargs
        )


def evaluate_imputation_model(
    model,
    val_sequences_missing,
    val_sequences_true,
    test_df,
    feature_cols,
    target_col,
    all_features,
    scalers,
    sequence_len=64,
    use_enhanced_preprocessing=True,
    **kwargs
):
    """
    Comprehensive evaluation for imputation-based models (original functionality).

    This function addresses the R² discrepancy by:
    1. Evaluating imputation performance on artificially missing values
    2. Evaluating prediction performance on completely held-out data
    3. Providing separate metrics for each task

    Args:
        model: Trained deep learning model (SAITS, BRITS, etc.)
        val_sequences_missing: Validation sequences with artificial missing values
        val_sequences_true: Original validation sequences
        test_df: Held-out test dataframe for prediction evaluation
        feature_cols: List of feature column names
        target_col: Target column name
        all_features: List of all features including target
        scalers: Dictionary of scalers for inverse transformation
        sequence_len: Length of sequences
        use_enhanced_preprocessing: Whether to use enhanced preprocessing
        **kwargs: Additional parameters

    Returns:
        dict: Comprehensive evaluation metrics for both tasks
    """

    results = {
        'imputation_metrics': {},
        'prediction_metrics': {},
        'combined_metrics': {},
        'mode': 'imputation_based'
    }

    # Convert to tensors if needed
    if not isinstance(val_sequences_missing, torch.Tensor):
        val_sequences_missing = torch.from_numpy(val_sequences_missing.astype(np.float32))
    if not isinstance(val_sequences_true, torch.Tensor):
        val_sequences_true = torch.from_numpy(val_sequences_true.astype(np.float32))

    target_idx = all_features.index(target_col)

    # ========== PART 1: IMPUTATION EVALUATION ==========
    # This evaluates the model's ability to fill in missing values
    # when surrounding context is available

    print("\nEvaluating Imputation Performance...")

    # Get model predictions for imputation
    with torch.no_grad():
        if hasattr(model, 'predict'):
            imputed_sequences = model.predict(val_sequences_missing)
        else:
            model.eval()
            imputed_sequences = model(val_sequences_missing)

    # Evaluate only on artificially missing values
    val_mask = torch.isnan(val_sequences_missing[:, :, target_idx])

    if val_mask.any():
        # Extract values for artificially missing positions only
        y_pred_imputation = imputed_sequences[:, :, target_idx][val_mask].detach().cpu().numpy()
        y_true_imputation = val_sequences_true[:, :, target_idx][val_mask].detach().cpu().numpy()

        # Remove any remaining NaNs
        valid_idx = ~np.isnan(y_true_imputation) & ~np.isnan(y_pred_imputation)
        y_pred_imputation = y_pred_imputation[valid_idx]
        y_true_imputation = y_true_imputation[valid_idx]

        if len(y_true_imputation) > 0:
            # Calculate imputation metrics
            mae_imp = mean_absolute_error(y_true_imputation, y_pred_imputation)
            r2_imp = r2_score(y_true_imputation, y_pred_imputation)
            rmse_imp = np.sqrt(mean_squared_error(y_true_imputation, y_pred_imputation))

            results['imputation_metrics'] = {
                'mae': mae_imp,
                'r2': r2_imp,
                'rmse': rmse_imp,
                'n_samples': len(y_true_imputation),
                'task_description': 'Filling artificial gaps with surrounding context'
            }

            print(f"Imputation Metrics (Artificial Missing Values):")
            print(f"   • MAE: {mae_imp:.4f}")
            print(f"   • R²: {r2_imp:.4f}")
            print(f"   • RMSE: {rmse_imp:.4f}")
            print(f"   • Samples: {len(y_true_imputation)}")

    # ========== PART 2: PREDICTION EVALUATION ==========
    # This evaluates the model's ability to predict values
    # without target context (more realistic scenario)

    print("\nEvaluating Prediction Performance...")

    # Prepare test data for prediction (no target context)
    from .data_handler import normalize_data, create_sequences

    # Normalize test data
    test_df_scaled, _ = normalize_data(test_df, all_features,
                                       use_enhanced=use_enhanced_preprocessing,
                                       scalers=scalers)

    # Create prediction scenario: features available, target completely missing
    test_pred_df = test_df_scaled.copy()
    test_pred_df[target_col] = np.nan  # Remove all target information

    # Create sequences for prediction
    test_sequences, test_metadata = create_sequences(
        test_pred_df, 'WELL', all_features,
        sequence_len=sequence_len,
        use_enhanced=use_enhanced_preprocessing
    )

    if test_sequences.shape[0] > 0:
        # Also create true sequences for comparison
        test_sequences_true, _ = create_sequences(
            test_df_scaled, 'WELL', all_features,
            sequence_len=sequence_len,
            use_enhanced=use_enhanced_preprocessing
        )

        # Convert to tensors
        test_tensor = torch.from_numpy(test_sequences.astype(np.float32))
        test_true_tensor = torch.from_numpy(test_sequences_true.astype(np.float32))

        # Get predictions
        with torch.no_grad():
            if hasattr(model, 'predict'):
                predicted_sequences = model.predict(test_tensor)
            else:
                model.eval()
                predicted_sequences = model(test_tensor)

        # Extract predictions and ground truth for all positions
        y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy().flatten()
        y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy().flatten()

        # Remove NaNs
        valid_idx = ~np.isnan(y_true_prediction) & ~np.isnan(y_pred_prediction)
        y_pred_prediction = y_pred_prediction[valid_idx]
        y_true_prediction = y_true_prediction[valid_idx]

        if len(y_true_prediction) > 0:
            # Calculate prediction metrics
            mae_pred = mean_absolute_error(y_true_prediction, y_pred_prediction)
            r2_pred = r2_score(y_true_prediction, y_pred_prediction)
            rmse_pred = np.sqrt(mean_squared_error(y_true_prediction, y_pred_prediction))

            results['prediction_metrics'] = {
                'mae': mae_pred,
                'r2': r2_pred,
                'rmse': rmse_pred,
                'n_samples': len(y_true_prediction),
                'task_description': 'Predicting without target context'
            }

            print(f"Prediction Metrics (No Target Context):")
            print(f"   • MAE: {mae_pred:.4f}")
            print(f"   • R²: {r2_pred:.4f}")
            print(f"   • RMSE: {rmse_pred:.4f}")
            print(f"   • Samples: {len(y_true_prediction)}")

    # ========== PART 3: COMBINED ANALYSIS ==========

    if results['imputation_metrics'] and results['prediction_metrics']:
        # Calculate performance drop
        r2_drop = results['imputation_metrics']['r2'] - results['prediction_metrics']['r2']

        results['combined_metrics'] = {
            'r2_discrepancy': r2_drop,
            'performance_ratio': results['prediction_metrics']['r2'] / results['imputation_metrics']['r2'] if results['imputation_metrics']['r2'] > 0 else 0,
            'task_complexity_increase': results['prediction_metrics']['mae'] / results['imputation_metrics']['mae'] if results['imputation_metrics']['mae'] > 0 else float('inf')
        }

        print(f"\nPerformance Analysis:")
        print(f"   • R² Drop (Imputation → Prediction): {r2_drop:.4f}")
        print(f"   • Performance Ratio: {results['combined_metrics']['performance_ratio']:.2%}")
        print(f"   • Task Complexity Increase: {results['combined_metrics']['task_complexity_increase']:.2f}x")

        # Provide interpretation
        if r2_drop > 0.2:
            print("\n⚠️ SIGNIFICANT PERFORMANCE DROP DETECTED!")
            print("   This indicates the model is optimized for imputation, not prediction.")
            print("   Consider:")
            print("   1. Using this model only for imputation tasks")
            print("   2. Training a separate model for prediction")
            print("   3. Using shallow ML models (XGBoost, etc.) for prediction")

    return results


def create_enhanced_evaluation_report(
    all_model_results,
    output_path="model_evaluation_report.txt"
):
    """
    Create a comprehensive evaluation report comparing all models
    on both imputation and prediction tasks.

    Args:
        all_model_results: Dict mapping model names to evaluation results
        output_path: Path to save the report
    """

    report_lines = [
        "=" * 80,
        "COMPREHENSIVE MODEL EVALUATION REPORT",
        "=" * 80,
        "",
        "This report separates imputation and prediction performance to address",
        "the R² discrepancy issue in deep learning models.",
        "",
        "-" * 80,
        ""
    ]

    # Create comparison tables
    imputation_data = []
    prediction_data = []

    for model_name, results in all_model_results.items():
        if 'imputation_metrics' in results and results['imputation_metrics']:
            imp = results['imputation_metrics']
            imputation_data.append({
                'Model': model_name,
                'MAE': f"{imp['mae']:.4f}",
                'R²': f"{imp['r2']:.4f}",
                'RMSE': f"{imp['rmse']:.4f}",
                'Samples': imp['n_samples']
            })

        if 'prediction_metrics' in results and results['prediction_metrics']:
            pred = results['prediction_metrics']
            prediction_data.append({
                'Model': model_name,
                'MAE': f"{pred['mae']:.4f}",
                'R²': f"{pred['r2']:.4f}",
                'RMSE': f"{pred['rmse']:.4f}",
                'Samples': pred['n_samples']
            })

    # Add imputation results
    report_lines.extend([
        "IMPUTATION PERFORMANCE (Filling Missing Values with Context)",
        "-" * 60,
        ""
    ])

    if imputation_data:
        df_imp = pd.DataFrame(imputation_data)
        report_lines.append(df_imp.to_string(index=False))
    else:
        report_lines.append("No imputation results available.")

    report_lines.extend(["", "", ""])

    # Add prediction results
    report_lines.extend([
        "PREDICTION PERFORMANCE (Predicting without Target Context)",
        "-" * 60,
        ""
    ])

    if prediction_data:
        df_pred = pd.DataFrame(prediction_data)
        report_lines.append(df_pred.to_string(index=False))
    else:
        report_lines.append("No prediction results available.")

    report_lines.extend(["", "", ""])

    # Add performance analysis
    report_lines.extend([
        "PERFORMANCE ANALYSIS",
        "-" * 60,
        ""
    ])

    for model_name, results in all_model_results.items():
        if 'combined_metrics' in results and results['combined_metrics']:
            cm = results['combined_metrics']
            report_lines.extend([
                f"{model_name}:",
                f"  • R² Discrepancy: {cm['r2_discrepancy']:.4f}",
                f"  • Performance Ratio: {cm['performance_ratio']:.2%}",
                f"  • Task Complexity Increase: {cm['task_complexity_increase']:.2f}x",
                ""
            ])

    # Add recommendations
    report_lines.extend([
        "",
        "RECOMMENDATIONS",
        "-" * 60,
        "",
        "1. For IMPUTATION tasks (filling gaps in existing data):",
        "   - Use deep learning models (SAITS, BRITS) if R² > 0.9",
        "   - These models excel at using bidirectional context",
        "",
        "2. For PREDICTION tasks (forecasting without target context):",
        "   - Use shallow ML models (XGBoost, CatBoost, LightGBM)",
        "   - These models better capture feature-target relationships",
        "",
        "3. Model Selection Guidelines:",
        "   - R² discrepancy > 0.2: Model is imputation-optimized",
        "   - R² discrepancy < 0.1: Model generalizes well to prediction",
        "   - Performance ratio < 80%: Consider alternative models for prediction",
        "",
        "=" * 80
    ])

    # Write report
    with open(output_path, 'w') as f:
        f.write('\n'.join(report_lines))

    print(f"\nReport saved to: {output_path}")

    return '\n'.join(report_lines)


def safe_array_alignment(arr1, arr2, operation_name="operation"):
    """
    Safely align two arrays to have compatible shapes for broadcasting.

    Args:
        arr1, arr2: Arrays to align
        operation_name: Name of operation for error reporting

    Returns:
        tuple: (aligned_arr1, aligned_arr2)
    """
    try:
        # Flatten both arrays
        flat1 = arr1.flatten() if hasattr(arr1, 'flatten') else np.array(arr1).flatten()
        flat2 = arr2.flatten() if hasattr(arr2, 'flatten') else np.array(arr2).flatten()

        print(f"   Shape alignment for {operation_name}:")
        print(f"      • Array 1 shape: {flat1.shape}")
        print(f"      • Array 2 shape: {flat2.shape}")

        # If shapes are different, truncate to minimum common size
        if flat1.shape != flat2.shape:
            min_size = min(len(flat1), len(flat2))
            print(f"      Shape mismatch detected! Truncating to {min_size} elements")
            flat1 = flat1[:min_size]
            flat2 = flat2[:min_size]
            print(f"      Aligned shapes: {flat1.shape}, {flat2.shape}")

        return flat1, flat2

    except Exception as e:
        print(f"      Error in array alignment for {operation_name}: {e}")
        # Return empty arrays as fallback
        return np.array([]), np.array([])


def evaluate_imputation_and_prediction(
    model,
    val_sequences_missing,
    val_sequences_true,
    test_df,
    feature_cols,
    target_col,
    all_features,
    scalers,
    sequence_len=64,
    use_enhanced_preprocessing=True
):
    """
    Properly evaluate deep learning models for both imputation and prediction tasks.

    This function addresses the R² discrepancy by:
    1. Evaluating imputation performance on artificially missing values
    2. Evaluating prediction performance on completely held-out data
    3. Providing separate metrics for each task

    Args:
        model: Trained deep learning model (SAITS, BRITS, etc.)
        val_sequences_missing: Validation sequences with artificial missing values
        val_sequences_true: Original validation sequences
        test_df: Held-out test dataframe for prediction evaluation
        feature_cols: List of feature column names
        target_col: Target column name
        all_features: List of all features including target
        scalers: Dictionary of scalers for inverse transformation
        sequence_len: Length of sequences
        use_enhanced_preprocessing: Whether to use enhanced preprocessing

    Returns:
        dict: Comprehensive evaluation metrics for both tasks
    """

    results = {
        'imputation_metrics': {},
        'prediction_metrics': {},
        'combined_metrics': {}
    }

    # Convert to tensors if needed
    if not isinstance(val_sequences_missing, torch.Tensor):
        val_sequences_missing = torch.from_numpy(val_sequences_missing.astype(np.float32))
    if not isinstance(val_sequences_true, torch.Tensor):
        val_sequences_true = torch.from_numpy(val_sequences_true.astype(np.float32))

    target_idx = all_features.index(target_col)

    # ========== PART 1: IMPUTATION EVALUATION ==========
    # This evaluates the model's ability to fill in missing values
    # when surrounding context is available

    print("\nEvaluating Imputation Performance...")

    # Get model predictions for imputation
    with torch.no_grad():
        if hasattr(model, 'predict'):
            imputed_sequences = model.predict(val_sequences_missing)
        else:
            model.eval()
            imputed_sequences = model(val_sequences_missing)

    # Evaluate only on artificially missing values
    val_mask = torch.isnan(val_sequences_missing[:, :, target_idx])

    if val_mask.any():
        # Extract values for artificially missing positions only
        y_pred_imputation = imputed_sequences[:, :, target_idx][val_mask].detach().cpu().numpy()
        y_true_imputation = val_sequences_true[:, :, target_idx][val_mask].detach().cpu().numpy()

        # Remove any remaining NaNs
        valid_idx = ~np.isnan(y_true_imputation) & ~np.isnan(y_pred_imputation)
        y_pred_imputation = y_pred_imputation[valid_idx]
        y_true_imputation = y_true_imputation[valid_idx]

        if len(y_true_imputation) > 0:
            # Calculate imputation metrics
            mae_imp = mean_absolute_error(y_true_imputation, y_pred_imputation)
            r2_imp = r2_score(y_true_imputation, y_pred_imputation)
            rmse_imp = np.sqrt(mean_squared_error(y_true_imputation, y_pred_imputation))

            results['imputation_metrics'] = {
                'mae': mae_imp,
                'r2': r2_imp,
                'rmse': rmse_imp,
                'n_samples': len(y_true_imputation),
                'task_description': 'Filling artificial gaps with surrounding context'
            }

            print(f"Imputation Metrics (Artificial Missing Values):")
            print(f"   • MAE: {mae_imp:.4f}")
            print(f"   • R²: {r2_imp:.4f}")
            print(f"   • RMSE: {rmse_imp:.4f}")
            print(f"   • Samples: {len(y_true_imputation)}")

    # ========== PART 2: PREDICTION EVALUATION ==========
    # This evaluates the model's ability to predict values
    # without target context (more realistic scenario)

    print("\nEvaluating Prediction Performance...")

    # Prepare test data for prediction (no target context)
    from .data_handler import normalize_data, create_sequences

    # Normalize test data
    test_df_scaled, _ = normalize_data(test_df, all_features,
                                       use_enhanced=use_enhanced_preprocessing,
                                       scalers=scalers)

    # Create prediction scenario: features available, target completely missing
    test_pred_df = test_df_scaled.copy()
    test_pred_df[target_col] = np.nan  # Remove all target information

    # Create sequences for prediction
    test_sequences, test_metadata = create_sequences(
        test_pred_df, 'WELL', all_features,
        sequence_len=sequence_len,
        use_enhanced=use_enhanced_preprocessing
    )

    if test_sequences.shape[0] > 0:
        # Also create true sequences for comparison
        test_sequences_true, _ = create_sequences(
            test_df_scaled, 'WELL', all_features,
            sequence_len=sequence_len,
            use_enhanced=use_enhanced_preprocessing
        )

        # Convert to tensors
        test_tensor = torch.from_numpy(test_sequences.astype(np.float32))
        test_true_tensor = torch.from_numpy(test_sequences_true.astype(np.float32))

        # Get predictions
        with torch.no_grad():
            if hasattr(model, 'predict'):
                predicted_sequences = model.predict(test_tensor)
            else:
                model.eval()
                predicted_sequences = model(test_tensor)

        # Extract predictions and ground truth for all positions
        y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy().flatten()
        y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy().flatten()

        # Remove NaNs
        valid_idx = ~np.isnan(y_true_prediction) & ~np.isnan(y_pred_prediction)
        y_pred_prediction = y_pred_prediction[valid_idx]
        y_true_prediction = y_true_prediction[valid_idx]

        if len(y_true_prediction) > 0:
            # Calculate prediction metrics
            mae_pred = mean_absolute_error(y_true_prediction, y_pred_prediction)
            r2_pred = r2_score(y_true_prediction, y_pred_prediction)
            rmse_pred = np.sqrt(mean_squared_error(y_true_prediction, y_pred_prediction))

            results['prediction_metrics'] = {
                'mae': mae_pred,
                'r2': r2_pred,
                'rmse': rmse_pred,
                'n_samples': len(y_true_prediction),
                'task_description': 'Predicting without target context'
            }

            print(f"Prediction Metrics (No Target Context):")
            print(f"   • MAE: {mae_pred:.4f}")
            print(f"   • R²: {r2_pred:.4f}")
            print(f"   • RMSE: {rmse_pred:.4f}")
            print(f"   • Samples: {len(y_true_prediction)}")

    # ========== PART 3: COMBINED ANALYSIS ==========

    if results['imputation_metrics'] and results['prediction_metrics']:
        # Calculate performance drop
        r2_drop = results['imputation_metrics']['r2'] - results['prediction_metrics']['r2']

        results['combined_metrics'] = {
            'r2_discrepancy': r2_drop,
            'performance_ratio': results['prediction_metrics']['r2'] / results['imputation_metrics']['r2'] if results['imputation_metrics']['r2'] > 0 else 0,
            'task_complexity_increase': results['prediction_metrics']['mae'] / results['imputation_metrics']['mae'] if results['imputation_metrics']['mae'] > 0 else float('inf')
        }

        print(f"\nPerformance Analysis:")
        print(f"   • R² Drop (Imputation → Prediction): {r2_drop:.4f}")
        print(f"   • Performance Ratio: {results['combined_metrics']['performance_ratio']:.2%}")
        print(f"   • Task Complexity Increase: {results['combined_metrics']['task_complexity_increase']:.2f}x")

        # Provide interpretation
        if r2_drop > 0.2:
            print("\n⚠️ SIGNIFICANT PERFORMANCE DROP DETECTED!")
            print("   This indicates the model is optimized for imputation, not prediction.")
            print("   Consider:")
            print("   1. Using this model only for imputation tasks")
            print("   2. Training a separate model for prediction")
            print("   3. Using shallow ML models (XGBoost, etc.) for prediction")

    return results


def create_enhanced_evaluation_report(
    all_model_results,
    output_path="model_evaluation_report.txt"
):
    """
    Create a comprehensive evaluation report comparing all models
    on both imputation and prediction tasks.

    Args:
        all_model_results: Dict mapping model names to evaluation results
        output_path: Path to save the report
    """

    report_lines = [
        "=" * 80,
        "COMPREHENSIVE MODEL EVALUATION REPORT",
        "=" * 80,
        "",
        "This report separates imputation and prediction performance to address",
        "the R² discrepancy issue in deep learning models.",
        "",
        "-" * 80,
        ""
    ]

    # Create comparison tables
    imputation_data = []
    prediction_data = []

    for model_name, results in all_model_results.items():
        if 'imputation_metrics' in results and results['imputation_metrics']:
            imp = results['imputation_metrics']
            imputation_data.append({
                'Model': model_name,
                'MAE': f"{imp['mae']:.4f}",
                'R²': f"{imp['r2']:.4f}",
                'RMSE': f"{imp['rmse']:.4f}",
                'Samples': imp['n_samples']
            })

        if 'prediction_metrics' in results and results['prediction_metrics']:
            pred = results['prediction_metrics']
            prediction_data.append({
                'Model': model_name,
                'MAE': f"{pred['mae']:.4f}",
                'R²': f"{pred['r2']:.4f}",
                'RMSE': f"{pred['rmse']:.4f}",
                'Samples': pred['n_samples']
            })

    # Add imputation results
    report_lines.extend([
        "IMPUTATION PERFORMANCE (Filling Missing Values with Context)",
        "-" * 60,
        ""
    ])

    if imputation_data:
        df_imp = pd.DataFrame(imputation_data)
        report_lines.append(df_imp.to_string(index=False))
    else:
        report_lines.append("No imputation results available.")

    report_lines.extend(["", "", ""])

    # Add prediction results
    report_lines.extend([
        "PREDICTION PERFORMANCE (Predicting without Target Context)",
        "-" * 60,
        ""
    ])

    if prediction_data:
        df_pred = pd.DataFrame(prediction_data)
        report_lines.append(df_pred.to_string(index=False))
    else:
        report_lines.append("No prediction results available.")

    report_lines.extend(["", "", ""])

    # Add performance analysis
    report_lines.extend([
        "PERFORMANCE ANALYSIS",
        "-" * 60,
        ""
    ])

    for model_name, results in all_model_results.items():
        if 'combined_metrics' in results and results['combined_metrics']:
            cm = results['combined_metrics']
            report_lines.extend([
                f"{model_name}:",
                f"  • R² Discrepancy: {cm['r2_discrepancy']:.4f}",
                f"  • Performance Ratio: {cm['performance_ratio']:.2%}",
                f"  • Task Complexity Increase: {cm['task_complexity_increase']:.2f}x",
                ""
            ])

    # Add recommendations
    report_lines.extend([
        "",
        "RECOMMENDATIONS",
        "-" * 60,
        "",
        "1. For IMPUTATION tasks (filling gaps in existing data):",
        "   - Use deep learning models (SAITS, BRITS) if R² > 0.9",
        "   - These models excel at using bidirectional context",
        "",
        "2. For PREDICTION tasks (forecasting without target context):",
        "   - Use shallow ML models (XGBoost, CatBoost, LightGBM)",
        "   - These models better capture feature-target relationships",
        "",
        "3. Model Selection Guidelines:",
        "   - R² discrepancy > 0.2: Model is imputation-optimized",
        "   - R² discrepancy < 0.1: Model generalizes well to prediction",
        "   - Performance ratio < 80%: Consider alternative models for prediction",
        "",
        "=" * 80
    ])

    # Write report
    with open(output_path, 'w') as f:
        f.write('\n'.join(report_lines))

    print(f"\n📄 Report saved to: {output_path}")

    return '\n'.join(report_lines)

def impute_logs(df, feature_cols, target_col, models_to_run, well_cfg, prediction_mode):
    """Main imputation routine."""
    res = df.copy()
    feat_set = feature_cols + ['MD']
    print(f'--- Target Log: {target_col} ---')

    # Train data selection
    if well_cfg['mode'] == 'separated':
        train_df = res[res['WELL'].isin(well_cfg['training_wells'])]
    else:
        train_df = res
    train = train_df[train_df[target_col].notna()].copy()

    if train.empty:
        print("No training data.")
        return res, {}

    # Use forward-fill and back-fill to handle missing data in the feature set.
    # This is a more appropriate method for time-series data than mean imputation.
    X = train[feat_set].ffill().bfill()
    y = train[target_col]
    X_tr, X_val, y_tr, y_val = train_test_split(X, y, test_size=0.25, random_state=42)

    evals, trained = [], {}
    for name, model in models_to_run.items():
        model_config = MODEL_REGISTRY.get(name, {})
        model_type = model_config.get('type')

        try:
            if model_type in ['deep', 'deep_advanced']:
                # For deep models, call the specialized function
                hparams = {hp: details['default'] for hp, details in model_config['hyperparameters'].items()}
                
                # Ensure n_features is correctly set from the data
                hparams['n_features'] = len(feature_cols) + 1 # features + target

                # Add diagnostic flags for debugging
                if 'mrnn' in name.lower():
                    hparams['debug_grad'] = True
                    hparams['disable_checkpoint'] = True
                
                imputed_df, model_results = impute_logs_deep(df, feature_cols, target_col, model_config, hparams)
                
                if model_results and model_results.get('evaluations'):
                    evals.extend(model_results['evaluations'])
                    # Use the imputed dataframe from the deep learning model for subsequent steps
                    res = imputed_df
                    best_model_name = model_results.get('best_model_name')
                    if best_model_name and model_results.get('trained_models'):
                        trained[best_model_name] = model_results['trained_models'][best_model_name]
                    
                    print(f"{name}: MAE={model_results['evaluations'][0]['mae']:.3f}, R2={model_results['evaluations'][0]['r2']:.3f}")
                else:
                    print(f"{name} failed to produce results.")
                
                # Since deep learning models handle their own imputation loop, we can break here
                # if we only intend to run one deep model at a time this way.
                # If multiple deep models can be run in this loop, this break might need reconsideration.
                break
            
            else:
                # Handle base and shallow models with GPU optimization
                try:
                    # Use GPU-optimized model creation if available
                    model_key = None
                    for key, config in MODEL_REGISTRY.items():
                        if config.get('model_class') == type(model):
                            model_key = key
                            break

                    if model_key:
                        # Get hyperparameters from the model
                        current_params = model.get_params()
                        # Create GPU-optimized version
                        optimized_model = create_gpu_optimized_model(model_key, current_params)
                        model = optimized_model

                    if 'early_stopping_rounds' in model.get_params():
                        model.fit(X_tr, y_tr, eval_set=[(X_val, y_val)], verbose=False)
                    else:
                        model.fit(X_tr, y_tr)

                    ev = evaluate_model_comprehensive(model, X_tr, y_tr, X_val, y_val)
                    ev['model_name'] = name

                    # Extract mathematical equation for MLR models
                    if hasattr(model, 'get_equation_for_reporting'):
                        equation_info = model.get_equation_for_reporting(target_col)
                        if equation_info:
                            ev['mathematical_equation'] = equation_info
                            print(f'\nMathematical Equation for {name}:')
                            print(f'   {equation_info["equation_text"]}')

                    evals.append(ev)
                    trained[name] = model
                    print(f'{name}: MAE={ev["mae"]:.3f}, R2={ev["r2"]:.3f}')

                except Exception as gpu_error:
                    print(f"⚠️ GPU optimization failed for {name}: {gpu_error}")
                    print(f"   Falling back to original model...")

                    # Fallback to original model
                    if 'early_stopping_rounds' in model.get_params():
                        model.fit(X_tr, y_tr, eval_set=[(X_val, y_val)], verbose=False)
                    else:
                        model.fit(X_tr, y_tr)

                    ev = evaluate_model_comprehensive(model, X_tr, y_tr, X_val, y_val)
                    ev['model_name'] = name

                    # Extract mathematical equation for MLR models
                    if hasattr(model, 'get_equation_for_reporting'):
                        equation_info = model.get_equation_for_reporting(target_col)
                        if equation_info:
                            ev['mathematical_equation'] = equation_info
                            print(f'\nMathematical Equation for {name}:')
                            print(f'   {equation_info["equation_text"]}')

                    evals.append(ev)
                    trained[name] = model
                    print(f'{name}: MAE={ev["mae"]:.3f}, R2={ev["r2"]:.3f}')

        except Exception as e:
            print(f"❌ {name} failed during training/imputation: {e}")
            import traceback
            traceback.print_exc()
            continue

    if not evals:
        print("All models failed.")
        return res, {}

    evals.sort(key=lambda x: x['composite_score'])
    best_name = evals[0]['model_name']
    best_model = trained[best_name]
    print(f'Best model: {best_name}')

    # Prediction
    if well_cfg['mode'] == 'separated':
        pred_mask = res['WELL'].isin(well_cfg['prediction_wells'])
    else:
        pred_mask = pd.Series(True, index=res.index)

    # Use forward-fill and back-fill to handle missing data in the prediction set.
    X_pred = res.loc[pred_mask, feat_set].ffill().bfill()
    preds = best_model.predict(X_pred) if not X_pred.empty else np.array([])

    full_pred = pd.Series(np.nan, index=res.index)
    full_pred.loc[pred_mask] = preds

    imp_col = f'{target_col}_imputed'
    pred_col = f'{target_col}_pred'
    err_col = f'{target_col}_error'

    res[pred_col] = full_pred
    if prediction_mode == 3:
        res[imp_col] = full_pred
    else:
        res[imp_col] = res[target_col].fillna(full_pred)

    mask_orig = res[target_col].notna() & res[pred_col].notna()
    res[err_col] = np.nan
    res.loc[mask_orig, err_col] = np.abs(res.loc[mask_orig, target_col] - res.loc[mask_orig, pred_col])

    return res, {'target': target_col, 'evaluations': evals, 'best_model_name': best_name, 'trained_models': trained}

def prepare_prediction_data(df_scaled, feature_cols, target_col):
    """
    Prepare data for prediction by filling feature columns while preserving target NaNs.
    This prevents data leakage by ensuring the model only sees features, not target values.

    Args:
        df_scaled: Scaled dataframe with all features including target
        feature_cols: List of feature column names (excluding target)
        target_col: Name of target column to predict

    Returns:
        DataFrame with features filled and target NaNs preserved
    """
    prediction_input_df = df_scaled.copy()

    # Forward-fill and back-fill feature columns to create continuous sequences
    # This allows create_sequences to work while preserving temporal structure
    prediction_input_df[feature_cols] = prediction_input_df[feature_cols].ffill().bfill()

    # Mask the *entire* target column so the model cannot peek at ground truth
    # This prevents data leakage during inference.
    prediction_input_df[target_col] = np.nan

    print(f"Data preparation for prediction:")
    print(f"  - Feature columns filled: {feature_cols}")
    print(f"  - Target column '{target_col}' NaNs preserved")
    print(f"  - Original NaN count in target: {df_scaled[target_col].isna().sum()}")
    print(f"  - Preserved NaN count in target: {prediction_input_df[target_col].isna().sum()}")

    return prediction_input_df


def create_flexible_split(df, well_col='WELL', depth_col='MD',
                          train_wells=None, test_wells=None,
                          val_depth_ratio=0.3, random_state=42):
    """
    Creates a highly flexible train/validation/test split.

    It allows for manual specification of training and test wells. A temporal split
    is then applied to the training wells to create a validation set.

    Args:
        df: Input dataframe.
        well_col: Name of the well identifier column.
        depth_col: Name of the depth/time column.
        train_wells: (Optional) A list of well names to use for training/validation.
        test_wells: (Optional) A list of well names to hold out for the test set.
        val_depth_ratio: Proportion of depth to use for validation within the training wells.
        random_state: Random seed for reproducibility.

    Returns:
        train_df, val_df, test_df
    """
    if train_wells is None or test_wells is None:
        raise ValueError("You must provide a list of 'train_wells' and 'test_wells'.")

    # Ensure no overlap between train and test wells
    if set(train_wells) & set(test_wells):
        raise ValueError("Train and test wells must not overlap.")

    # The final test set is composed of the user-specified held-out wells
    test_df = df[df[well_col].isin(test_wells)].copy()

    train_dfs, val_dfs = [], []

    # Apply temporal split on the user-specified training wells
    # Use adaptive minimum threshold based on validation ratio
    # More aggressive threshold reduction for very small datasets
    base_threshold = max(8, int(15 * val_depth_ratio + 8))  # Reduced from 20 to 15, minimum from 10 to 8

    # Further reduce for very small validation ratios
    if val_depth_ratio <= 0.15:
        min_well_size = max(6, base_threshold - 3)  # Even lower for 15% validation
    else:
        min_well_size = base_threshold

    print(f"   Minimum well size threshold: {min_well_size} (validation ratio: {val_depth_ratio:.1%})")

    for well in train_wells:
        well_df = df[df[well_col] == well].sort_values(depth_col)
        if len(well_df) < min_well_size:
            print(f"Warning: Well '{well}' has insufficient data ({len(well_df)} < {min_well_size}) and will be skipped.")
            continue

        # Split the training well into a shallow part (for training) and a deeper part (for validation)
        val_start_index = int(len(well_df) * (1 - val_depth_ratio))

        train_dfs.append(well_df.iloc[:val_start_index])
        val_dfs.append(well_df.iloc[val_start_index:])

    train_df = pd.concat(train_dfs, ignore_index=True) if train_dfs else pd.DataFrame()
    val_df = pd.concat(val_dfs, ignore_index=True) if val_dfs else pd.DataFrame()

    print("Flexible Split Report:")
    print(f"  - Wells for Training/Validation: {len(train_wells)}")
    print(f"  - Wells for Final Testing: {len(test_wells)}")
    print(f"  - Train Samples (Shallow part of train wells): {len(train_df)}")
    print(f"  - Validation Samples (Deeper part of train wells): {len(val_df)}")
    print(f"  - Test Samples (Entirely separate wells): {len(test_df)}")

    return train_df, val_df, test_df


def impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing=True):
    """
    Main imputation routine for a single deep learning model.

    **FIXED VERSION** - Prevents data leakage by:
    1. Properly masking target values during prediction
    2. Only evaluating on artificially missing values
    3. Correct re-assembly without leaking original values
    """
    print(f"--- Running Deep Learning Model: {model_config['name']} ---")

    # --- GPU PERFORMANCE OPTIMIZATIONS ---
    if torch.cuda.is_available():
        torch.backends.cudnn.benchmark = True
        print("CuDNN benchmark enabled for performance.")

    # 1. Data Preparation
    all_features = feature_cols + [target_col]
    
    # --- IMPROVED TRAIN/VALIDATION/TEST SPLIT ---
    # Use flexible splitting with automatic well assignment to prevent data leakage
    wells = df['WELL'].unique()

    # Automatically assign wells: 60% training, 20% validation (via temporal split), 20% test
    np.random.seed(42)  # For reproducible well assignment
    shuffled_wells = np.random.permutation(wells)

    n_wells = len(wells)
    test_split_idx = int(n_wells * 0.8)  # 80% for train/val, 20% for test

    train_wells_list = shuffled_wells[:test_split_idx].tolist()
    test_wells_list = shuffled_wells[test_split_idx:].tolist()

    print(f"Automatic well assignment:")
    print(f"  - Training/Validation wells: {train_wells_list}")
    print(f"  - Test wells: {test_wells_list}")

    # Use the new flexible split function with adaptive validation ratio
    # Reduce validation ratio for small datasets to ensure sufficient training data
    wells_median_size = np.median([len(df[df['WELL'] == well]) for well in train_wells_list])

    # More aggressive adaptation for very small datasets
    if wells_median_size < 30:
        adaptive_val_ratio = 0.15  # 15% for very small wells
    elif wells_median_size < 50:
        adaptive_val_ratio = 0.2   # 20% for small wells
    else:
        adaptive_val_ratio = 0.3   # 30% for larger wells

    print(f"   Using adaptive validation ratio: {adaptive_val_ratio:.1%} (median well size: {wells_median_size:.0f})")

    # Additional check for extremely small datasets - further reduce validation ratio
    min_well_size_in_training = min([len(df[df['WELL'] == well]) for well in train_wells_list])
    if min_well_size_in_training < 20:
        adaptive_val_ratio = max(0.1, adaptive_val_ratio - 0.05)  # Reduce by 5%, minimum 10%
        print(f"   🚨 Very small wells detected (min: {min_well_size_in_training}) - reduced validation ratio to {adaptive_val_ratio:.1%}")

    train_df, val_df, test_df = create_flexible_split(
        df,
        well_col='WELL',
        depth_col='MD',
        train_wells=train_wells_list,
        test_wells=test_wells_list,
        val_depth_ratio=adaptive_val_ratio,  # Adaptive validation ratio
        random_state=42
    )

    # Validate that we have sufficient data with detailed diagnostics
    if len(train_df) == 0 or len(val_df) == 0:
        # Calculate the minimum well size threshold used in create_flexible_split
        calculated_min_well_size = max(10, int(20 * adaptive_val_ratio + 10))

        print("❌ ERROR: Insufficient data for flexible splitting")
        print(f"   Training samples: {len(train_df)}")
        print(f"   Validation samples: {len(val_df)}")
        print(f"   Training wells attempted: {len(train_wells_list)}")
        print(f"   Minimum well size threshold: {calculated_min_well_size}")
        print(f"   Validation ratio: {adaptive_val_ratio:.1%}")

        # Add detailed well-by-well analysis
        print(f"\n🔍 Well-by-well analysis:")
        for well in train_wells_list:
            well_size = len(df[df['WELL'] == well])
            status = "✅ PASS" if well_size >= calculated_min_well_size else "❌ FAIL"
            print(f"   {well}: {well_size} rows {status}")

        print()
        print("🔧 SOLUTIONS:")
        print("   1. Reduce sequence_len in hyperparameters (try 32 or 16)")
        print("   2. Use 'conservative' optimization level")
        print("   3. Check data quality - ensure wells have continuous data")
        print("   4. Consider using fewer features if data is sparse")
        print(f"   5. Current threshold is {calculated_min_well_size} - consider lowering validation ratio")
        return df, None

    # 🔍 LEAKAGE DETECTION: Check for data leakage after splitting
    if DATA_LEAKAGE_DETECTION_AVAILABLE:
        print("\nRunning data leakage detection on splits...")
        leakage_results = comprehensive_leakage_check(
            train_df, val_df, test_df, feature_cols, target_col,
            well_col='WELL', depth_col='MD'
        )

        # If critical leakage is detected, warn but continue
        if leakage_results['overall_leakage_detected']:
            print("⚠️ Data leakage detected but continuing with training...")
            print("   Review the recommendations above to improve data quality.")
        print()  # Add spacing

    # Normalize training data and get scalers
    train_df_scaled, scalers = normalize_data(train_df, all_features, use_enhanced=use_enhanced_preprocessing)

    # Create sequences for training
    train_sequences_true, train_metadata = create_sequences(train_df_scaled, 'WELL', all_features,
                                               sequence_len=hparams['sequence_len'], use_enhanced=use_enhanced_preprocessing)
    
    # If no sequences created, try with smaller sequence lengths
    if train_sequences_true.shape[0] == 0:
        print("⚠️ No sequences created with default length. Trying smaller sequence lengths...")
        for smaller_len in [32, 16, 8]:
            print(f"   Trying sequence length: {smaller_len}")
            train_sequences_true, train_metadata = create_sequences(train_df_scaled, 'WELL', all_features,
                                                       sequence_len=smaller_len, use_enhanced=use_enhanced_preprocessing)
            if train_sequences_true.shape[0] > 0:
                print(f"Successfully created {train_sequences_true.shape[0]} sequences with length {smaller_len}")
                hparams['sequence_len'] = smaller_len  # Update for consistency
                break
        
        if train_sequences_true.shape[0] == 0:
            print("ERROR: No training sequences could be created even with smaller lengths.")
            print("   Trying with standard preprocessing instead of enhanced...")
            
            # Try with standard preprocessing
            train_df_scaled, scalers = normalize_data(train_df, all_features, use_enhanced=False)
            for seq_len in [hparams['sequence_len'], 32, 16, 8]:
                train_sequences_true, train_metadata = create_sequences(train_df_scaled, 'WELL', all_features,
                                                           sequence_len=seq_len, use_enhanced=False)
                if train_sequences_true.shape[0] > 0:
                    print(f"Standard preprocessing created {train_sequences_true.shape[0]} sequences with length {seq_len}")
                    hparams['sequence_len'] = seq_len
                    use_enhanced_preprocessing = False  # Switch to standard for consistency
                    break
            
            if train_sequences_true.shape[0] == 0:
                print("ERROR: Cannot create training sequences with any method. Cannot proceed.")
                print("   This might indicate:")
                print("   - Insufficient continuous data in wells")
                print("   - Too many missing values")
                print("   - Data quality issues")
                return df, None
        
    # Create sequences for validation (use the potentially adjusted sequence length)
    val_df_scaled, _ = normalize_data(val_df, all_features, use_enhanced=use_enhanced_preprocessing, scalers=scalers)
    val_sequences_true, val_metadata = create_sequences(val_df_scaled, 'WELL', all_features,
                                             sequence_len=hparams['sequence_len'], use_enhanced=use_enhanced_preprocessing)

    # Mode-aware data preparation
    use_prediction_only = hparams.get('use_prediction_only', False)
    if use_prediction_only:
        print("🚀 SKIPPING missing value introduction for prediction-only mode.")
        train_sequences_missing = train_sequences_true.copy()
        val_sequences_missing = val_sequences_true.copy()
    else:
        print("📚 Introducing missingness for imputation training.")
        train_sequences_missing = introduce_missingness(train_sequences_true, target_col_name=target_col,
                                                        feature_names=all_features, missing_rate=0.3, use_enhanced=use_enhanced_preprocessing)
        val_sequences_missing = introduce_missingness(val_sequences_true, target_col_name=target_col,
                                                      feature_names=all_features, missing_rate=0.3, use_enhanced=use_enhanced_preprocessing)

    # Convert to PyTorch tensors - ensure proper shape and type
    print(f"Converting sequences to tensors...")
    
    def to_tensor(data, name):
        print(f"   Processing {name}: current type is {type(data)}")
        if isinstance(data, pd.DataFrame):
            print(f"   Converting {name} from pandas.DataFrame to numpy array.")
            data = data.values
        elif not isinstance(data, np.ndarray):
            print(f"   Converting {name} from {type(data)} to numpy array.")
            data = np.array(data, dtype=np.float32)
        
        # Ensure dtype is float32
        if data.dtype != np.float32:
            print(f"   Changing {name} dtype from {data.dtype} to float32.")
            data = data.astype(np.float32)
            
        tensor = torch.from_numpy(data)
        print(f"   {name} tensor created with shape: {tensor.shape}, dtype: {tensor.dtype}")
        return tensor

    train_tensor = to_tensor(train_sequences_missing, "train_sequences_missing")
    truth_tensor = to_tensor(train_sequences_true, "train_sequences_true")
    val_train_tensor = to_tensor(val_sequences_missing, "val_sequences_missing")
    val_truth_tensor = to_tensor(val_sequences_true, "val_sequences_true")


    print(f"   Tensor shapes - Train: {train_tensor.shape}, Truth: {truth_tensor.shape}")

    # 2. Enhanced Model Initialization with Mode Detection
    hparams['n_features'] = len(all_features)
    # Only gate on actual registry availability; advanced models are independent of DEEP_MODELS_AVAILABLE
    if model_config.get('model_class') is None:
        print(f"Deep learning model {model_config.get('name', 'Unknown')} not available. Skipping.")
        return df, None

    # Enhanced model creation with mode-specific configuration
    model = create_enhanced_model_instance(model_config, hparams)

    # --- FIX: Force model initialization to create optimizer before use ---
    if hasattr(model, 'model') and getattr(model, 'model', None) is None and hasattr(model, '_initialize_model'):
        print("🔧 Forcing model initialization to prepare optimizer for schedulers...")
        model._initialize_model()
    # --- END FIX ---

    # 3. Training
    print("Training phase...")
    print(f"   About to train model with:")
    print(f"   - train_tensor type: {type(train_tensor)}, shape: {train_tensor.shape}, dtype: {train_tensor.dtype}")
    print(f"   - truth_tensor type: {type(truth_tensor)}, shape: {truth_tensor.shape}, dtype: {truth_tensor.dtype}")
    
    try:
        # --- TRAINING WITH MODEL-SPECIFIC INTERFACE ---
        model_type = model_config.get('type', 'deep')

        # Initialize training stability utilities
        print("🔧 Initializing training stability utilities...")
        from preprocessing.deep_model.stability_preprocessing import UniversalGradientClipper, AdaptiveLRScheduler
        
        # Determine model type for stability components
        stability_model_type = model_config.get('name', 'default').lower()
        if 'transformer' in stability_model_type:
            stability_model_type = 'transformer'
        elif 'saits' in stability_model_type:
            stability_model_type = 'saits'
        elif 'brits' in stability_model_type:
            stability_model_type = 'brits'


        # Initialize gradient clipper and (optionally) learning rate scheduler
        gradient_clipper = UniversalGradientClipper(model_type=stability_model_type, max_norm=hparams.get('gradient_clip_norm', None))

        # Detect PyPOTS-based models early (SAITS, BRITS)
        is_pypots_model = model_type == 'deep_advanced' and hasattr(model, '_prepare_data')

        # For PyPOTS models, skip external LR scheduler because the optimizer is managed internally by PyPOTS
        lr_scheduler = None
        if is_pypots_model:
            print(f"   ℹ️ Skipping AdaptiveLRScheduler for PyPOTS model ({stability_model_type}); optimizer is managed internally.")
        else:
            optimizer_for_scheduler = getattr(model, 'optimizer', None)
            if optimizer_for_scheduler:
                lr_scheduler = AdaptiveLRScheduler(
                    optimizer=optimizer_for_scheduler,
                    model_type=stability_model_type,
                    warmup_steps=hparams.get('warmup_steps', 1000),
                    total_steps=hparams.get('epochs', 100) * (train_tensor.shape[0] // hparams.get('batch_size', 32))
                )
                print(f"   ✅ AdaptiveLRScheduler initialized for {stability_model_type}")
            else:
                print(f"   ⚠️ Could not initialize AdaptiveLRScheduler: model.optimizer is None.")

        if lr_scheduler:
            print(f"   ✅ Gradient clipper ({stability_model_type}) and LR scheduler initialized")
        else:
            print(f"   ✅ Gradient clipper ({stability_model_type}) initialized")

        if is_pypots_model:
            # PyPOTS models expect tensors directly, not DataLoaders
            print(f"Training PyPOTS-based model: {model_config['name']}")
            print(f"   Using tensor interface with batch_size={hparams['batch_size']}, epochs={hparams['epochs']}")

            # PyPOTS models use their own internal batching
            model.fit(train_tensor, truth_tensor, epochs=hparams['epochs'], batch_size=hparams['batch_size'])

        else:
            # Other models that might support DataLoader interface
            print(f"Training with DataLoader interface: {model_config['name']}")
            
            # Optimize DataLoader parameters for better performance
            num_workers = hparams.get('num_workers', min(4, os.cpu_count() or 1))  # Use 4 workers or available CPUs
            pin_memory = torch.cuda.is_available()  # Enable for GPU training
            persistent_workers = num_workers > 0 and torch.cuda.is_available()  # Keep workers alive between epochs
            prefetch_factor = 2 if num_workers > 0 else 2  # Prefetch batches for smoother training
            
            train_dataset = TensorDataset(train_tensor, truth_tensor)
            train_loader = DataLoader(
                train_dataset,
                batch_size=hparams['batch_size'],
                shuffle=True,
                num_workers=num_workers,
                pin_memory=pin_memory,
                persistent_workers=persistent_workers,
                prefetch_factor=prefetch_factor,
                drop_last=True if len(train_dataset) > hparams['batch_size'] else False  # Drop incomplete batches for consistency
            )
            print(f"   DataLoader configured with batch_size={hparams['batch_size']}, num_workers={num_workers}, pin_memory={pin_memory}")

            # Prepare parameters for models that support them
            fit_params = {'epochs': hparams['epochs']}

            # Only add gradient_accumulation_steps if the model supports it
            if 'gradient_accumulation_steps' in hparams and hasattr(model, 'supports_gradient_accumulation'):
                fit_params['gradient_accumulation_steps'] = hparams['gradient_accumulation_steps']
                print(f"   Using gradient accumulation steps: {hparams['gradient_accumulation_steps']}")

            # Call fit with appropriate parameters
            model_name = model_config.get('name')
            if hasattr(model, 'fit') and len(model.fit.__code__.co_varnames) > 3:
                # Model supports additional parameters (and likely a DataLoader)
                model.fit(train_loader, **fit_params)
            else:
                # Model only supports basic parameters
                model.fit(train_tensor, truth_tensor, epochs=hparams['epochs'])

    except Exception as e:
        print(f"Error during training: {e}")
        print(f"   Train tensor type: {type(train_tensor)}, shape: {train_tensor.shape}, dtype: {train_tensor.dtype}")
        print(f"   Truth tensor type: {type(truth_tensor)}, shape: {truth_tensor.shape}, dtype: {truth_tensor.dtype}")
        
        # Try to diagnose the issue more specifically
        if hasattr(model, 'model') and model.model is not None:
            print(f"   Model internal state: {type(model.model)}")
        
        # Check if the issue is in the model's internal data handling
        print("   Attempting to debug the model's fit method...")
        
        # Re-raise the original exception with more context
        raise RuntimeError(f"{model_config['name']} failed: {str(e)}") from e

    # 4. Evaluation on Validation Set - ENHANCED FOR PROPER EVALUATION
    print("Enhanced Evaluation Phase...")

    # Store both imputation and prediction metrics
    imputation_metrics = {'mae': -1, 'r2': -1, 'rmse': -1}
    prediction_metrics = {'mae': -1, 'r2': -1, 'rmse': -1}
    comprehensive_evaluation = {}

    if val_train_tensor.shape[0] > 0:
        # === IMPUTATION EVALUATION (existing code) ===
        # Use memory-optimized prediction for validation if available
        if hasattr(model, 'predict_large_dataset'):
            imputed_val_tensor = model.predict_large_dataset(val_train_tensor)
        else:
            imputed_val_tensor = model.predict(val_train_tensor)
        target_idx = all_features.index(target_col)

        # Only evaluate on artificially missing values
        val_mask = torch.isnan(val_train_tensor[:, :, target_idx])

        if val_mask.any():
            # Extract predictions and ground truth for artificially missing values only
            y_pred_val = imputed_val_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()
            y_true_val = val_truth_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()

            # Additional validation: ensure we have valid data
            valid_indices = ~np.isnan(y_true_val) & ~np.isnan(y_pred_val)
            y_pred_val = y_pred_val[valid_indices]
            y_true_val = y_true_val[valid_indices]

            if len(y_true_val) > 0:
                imputation_metrics['mae'] = mean_absolute_error(y_true_val, y_pred_val)
                imputation_metrics['r2'] = r2_score(y_true_val, y_pred_val)
                imputation_metrics['rmse'] = np.sqrt(mean_squared_error(y_true_val, y_pred_val))

                print(f"Imputation Metrics (Artificial Missing Values):")
                print(f"   • MAE: {imputation_metrics['mae']:.4f}")
                print(f"   • R²: {imputation_metrics['r2']:.4f}")
                print(f"   • RMSE: {imputation_metrics['rmse']:.4f}")
                print(f"   • Evaluated Points: {len(y_true_val)}")

        # === PREDICTION EVALUATION (new code) ===
        # Create sequences where entire target column is missing
        val_df_pred = val_df_scaled.copy()
        val_df_pred[target_col] = np.nan  # Mask entire target

        val_pred_sequences, _ = create_sequences(val_df_pred, 'WELL', all_features,
                                                 sequence_len=hparams['sequence_len'],
                                                 use_enhanced=use_enhanced_preprocessing)

        if val_pred_sequences.shape[0] > 0:
            val_pred_tensor = torch.from_numpy(val_pred_sequences.astype(np.float32))

            # Get predictions
            if hasattr(model, 'predict_large_dataset'):
                pred_output = model.predict_large_dataset(val_pred_tensor)
            else:
                pred_output = model.predict(val_pred_tensor)

            # Compare with ground truth
            y_pred_full = pred_output[:, :, target_idx].detach().cpu().numpy().flatten()
            y_true_full = val_truth_tensor[:, :, target_idx].detach().cpu().numpy().flatten()

            # Remove NaNs
            valid_idx = ~np.isnan(y_true_full) & ~np.isnan(y_pred_full)
            y_pred_full = y_pred_full[valid_idx]
            y_true_full = y_true_full[valid_idx]

            if len(y_true_full) > 0:
                prediction_metrics['mae'] = mean_absolute_error(y_true_full, y_pred_full)
                prediction_metrics['r2'] = r2_score(y_true_full, y_pred_full)
                prediction_metrics['rmse'] = np.sqrt(mean_squared_error(y_true_full, y_pred_full))

                print(f"\nPrediction Metrics (No Target Context):")
                print(f"   • MAE: {prediction_metrics['mae']:.4f}")
                print(f"   • R²: {prediction_metrics['r2']:.4f}")
                print(f"   • RMSE: {prediction_metrics['rmse']:.4f}")
                print(f"   • Evaluated Points: {len(y_true_full)}")

        # === COMPREHENSIVE EVALUATION ===
        # Use the enhanced evaluation function if test data is available
        if len(test_df) > 0:
            try:
                comprehensive_evaluation = evaluate_imputation_and_prediction(
                    model=model,
                    val_sequences_missing=val_train_tensor,
                    val_sequences_true=val_truth_tensor,
                    test_df=test_df,
                    feature_cols=feature_cols,
                    target_col=target_col,
                    all_features=all_features,
                    scalers=scalers,
                    sequence_len=hparams['sequence_len'],
                    use_enhanced_preprocessing=use_enhanced_preprocessing
                )

                # Update metrics with comprehensive evaluation if available
                if comprehensive_evaluation.get('imputation_metrics'):
                    imp_comp = comprehensive_evaluation['imputation_metrics']
                    imputation_metrics.update(imp_comp)

                if comprehensive_evaluation.get('prediction_metrics'):
                    pred_comp = comprehensive_evaluation['prediction_metrics']
                    prediction_metrics.update(pred_comp)

            except Exception as e:
                print(f"⚠️ Comprehensive evaluation failed: {e}")
                print("   Continuing with basic evaluation...")

    # Analyze and report discrepancy
    if imputation_metrics['r2'] > 0 and prediction_metrics['r2'] > 0:
        r2_discrepancy = imputation_metrics['r2'] - prediction_metrics['r2']

        if r2_discrepancy > 0.2:
            print(f"\nPERFORMANCE DISCREPANCY DETECTED!")
            print(f"   • Imputation R²: {imputation_metrics['r2']:.4f}")
            print(f"   • Prediction R²: {prediction_metrics['r2']:.4f}")
            print(f"   • Discrepancy: {r2_discrepancy:.4f}")
            print(f"\n   This model is optimized for imputation, not prediction.")
            print(f"   Consider using shallow ML models for pure prediction tasks.")

        # Enhanced data leakage detection warning
        if imputation_metrics['r2'] > 0.95:
            print("🚨 CRITICAL WARNING: Suspiciously high R² (>0.95) - potential data leakage detected!")
            print("   This may indicate the model is seeing ground truth during training.")

            # Additional leakage check if available
            if DATA_LEAKAGE_DETECTION_AVAILABLE:
                print("   Running additional correlation-based leakage detection...")
                correlation_check = detect_perfect_correlation_leakage(
                    train_df, val_df, test_df, feature_cols, target_col, threshold=0.9
                )
                if correlation_check['leakage_detected']:
                    print("   🚨 Correlation-based leakage confirmed!")
                else:
                    print("   ✅ No correlation-based leakage found - high performance may be legitimate")

    else:
        if val_train_tensor.shape[0] == 0:
            print("⚠️  Warning: No validation sequences to evaluate.")
            print("   This may indicate insufficient validation data or sequence creation issues.")
        else:
            print("⚠️  Warning: Could not evaluate model performance.")
            print("   This may indicate issues with the evaluation process.")

    # 5. Imputation (Prediction on all data) - **FIXED FOR DATA LEAKAGE**
    print("Prediction phase (full dataset)...")

    # Prepare the full dataset for prediction
    df_scaled, full_scalers = normalize_data(df, all_features, use_enhanced=use_enhanced_preprocessing)

    # **BUG FIX**: Use helper function to properly prepare prediction data
    prediction_input_df = prepare_prediction_data(df_scaled, feature_cols, target_col)

    # Now, create sequences from this feature-filled dataframe.
    # The sequences will contain NaNs in the target column, which is what the model must predict.
    sequences_to_predict, metadata = create_sequences(prediction_input_df, 'WELL', all_features,
                                                      sequence_len=hparams['sequence_len'], use_enhanced=use_enhanced_preprocessing)
    if sequences_to_predict.shape[0] == 0:
        print("ERROR: No sequences created from the full dataset for prediction.")
        return df, None

    # Predict on the sequences (which now correctly contain NaNs in target column)
    prediction_tensor = torch.from_numpy(sequences_to_predict.astype(np.float32))
    print(f"Prediction tensor shape: {prediction_tensor.shape}, dtype: {prediction_tensor.dtype}")

    # Use memory-optimized prediction for large datasets
    if hasattr(model, 'predict_large_dataset'):
        print(f"🧠 Using memory-optimized prediction for {prediction_tensor.shape[0]:,} samples")
        imputed_full_tensor = model.predict_large_dataset(prediction_tensor)
    else:
        print(f"📦 Using standard prediction for {prediction_tensor.shape[0]:,} samples")
        imputed_full_tensor = model.predict(prediction_tensor)
    print(f"Model output tensor shape: {imputed_full_tensor.shape}, dtype: {imputed_full_tensor.dtype}")

    imputed_sequences = imputed_full_tensor.detach().cpu().numpy()
    print(f"Converted to numpy - shape: {imputed_sequences.shape}, dtype: {imputed_sequences.dtype}")
    print(f"Number of sequences to process: {len(metadata)}")

    # 6. Post-processing & Re-assembly - **FIXED FOR DATA LEAKAGE**
    print("Post-processing and re-assembling results...")

    # Create a new dataframe to hold the imputed values to avoid confusion
    imputed_df_unscaled = df.copy()

    # Create placeholder for averaging predictions on overlapping sequences
    pred_sum_df = pd.DataFrame(
        np.zeros((len(df.index), len(all_features)), dtype=np.float16),
        index=df.index, columns=all_features
    )
    pred_count_df = pd.DataFrame(
        np.zeros((len(df.index), len(all_features)), dtype=np.int32),
        index=df.index, columns=all_features
    )

    # Add progress bar to reconstruction loop
    for i, seq_meta in enumerate(tqdm(metadata, desc="Reconstructing sequences", unit="seq")):
        original_indices = seq_meta['original_indices']
        # This is the predicted sequence, in SCALED format
        predicted_sequence_scaled = imputed_sequences[i]

        # Debug information for troubleshooting
        if i == 0:  # Print debug info for first sequence only
            print(f"Debug info for sequence {i}:")
            print(f"  predicted_sequence_scaled shape: {predicted_sequence_scaled.shape}")
            print(f"  predicted_sequence_scaled dtype: {predicted_sequence_scaled.dtype}")
            print(f"  original_indices length: {len(original_indices)}")
            print(f"  all_features length: {len(all_features)}")
            print(f"  pred_sum_df slice shape: {pred_sum_df.loc[original_indices, all_features].shape}")
            print(f"  pred_sum_df dtype: {pred_sum_df.dtypes.iloc[0]}")

        try:
            # Use numpy array directly to avoid DataFrame overhead and dtype casts
            add_vals = predicted_sequence_scaled.astype(np.float16, copy=False)

            # Accumulate sums using numpy with float16 to match predictions (minimize casting)
            current_vals = pred_sum_df.loc[original_indices, all_features].values
            # If any NaNs remain in predictions, treat them as zeros in accumulation
            np.nan_to_num(add_vals, copy=False, nan=0.0, posinf=0.0, neginf=0.0)
            pred_sum_df.loc[original_indices, all_features] = current_vals + add_vals
            # Increment counts
            pred_count_df.loc[original_indices, all_features] = (
                pred_count_df.loc[original_indices, all_features] + 1
            )

        except Exception as e:
            print(f"❌ Error processing sequence {i}: {e}")
            print(f"   Sequence shape: {predicted_sequence_scaled.shape}")
            print(f"   Indices: {original_indices[:5]}... (showing first 5)")
            print(f"   Features: {all_features}")

            # Fallback: try element-wise assignment
            try:
                for j, idx in enumerate(original_indices):
                    for k, col in enumerate(all_features):
                        pred_sum_df.loc[idx, col] += np.float16(predicted_sequence_scaled[j, k])
                        pred_count_df.loc[idx, col] += 1
                print(f"✅ Fallback assignment successful for sequence {i}")
            except Exception as fallback_e:
                print(f"❌ Fallback also failed for sequence {i}: {fallback_e}")
                raise RuntimeError(f"Failed to process sequence {i} in post-processing") from e

    # Validate post-processing results
    print(f"Post-processing validation:")
    print(f"  pred_sum_df shape: {pred_sum_df.shape}")
    print(f"  pred_count_df shape: {pred_count_df.shape}")
    print(f"  pred_sum_df non-zero elements: {(pred_sum_df != 0).sum().sum()}")
    print(f"  pred_count_df non-zero elements: {(pred_count_df != 0).sum().sum()}")
    print(f"  Max count value: {pred_count_df.max().max()}")

    # Calculate the average prediction and avoid division by zero
    # Compute average in float32 while avoiding division by zero
    counts_float = pred_count_df.values.astype(np.float32, copy=False)
    with np.errstate(invalid='ignore', divide='ignore'):
        counts_float[counts_float == 0] = np.nan
        avg_vals = pred_sum_df.values.astype(np.float32, copy=False) / counts_float
    avg_pred_df_scaled = pd.DataFrame(avg_vals, index=df.index, columns=all_features).astype(np.float32)
    print(f"  avg_pred_df_scaled shape: {avg_pred_df_scaled.shape}")
    print(f"  avg_pred_df_scaled dtype: {avg_pred_df_scaled.dtypes.iloc[0]}")
    print(f"  avg_pred_df_scaled non-NaN elements: {avg_pred_df_scaled.notna().sum().sum()}")

    # Inverse transform the predictions to get back to the original scale
    imputed_df_scaled = avg_pred_df_scaled.copy()
    for col in all_features:
        if col in full_scalers and full_scalers[col] is not None:
            valid_mask = imputed_df_scaled[col].notna()
            if valid_mask.any():
                col_data = imputed_df_scaled.loc[valid_mask, [col]].values.astype(np.float64, copy=False)
                # Inverse transform and flatten to 1D for proper assignment
                inverse_transformed = full_scalers[col].inverse_transform(col_data)
                if inverse_transformed.ndim > 1:
                    inverse_transformed = inverse_transformed.flatten()
                # Cast back to float32 to maintain consistent dtype and memory efficiency
                imputed_df_scaled.loc[valid_mask, col] = inverse_transformed.astype(np.float32)

    # Final assignment to result dataframe
    res_df = df.copy()
    pred_col = f'{target_col}_pred'
    imp_col = f'{target_col}_imputed'
    err_col = f'{target_col}_error'

    # The '_pred' column shows the model's output for ALL points
    res_df[pred_col] = imputed_df_scaled[target_col].astype(np.float32)

    # The '_imputed' column shows the original data, with NaNs filled by the prediction
    res_df[imp_col] = res_df[target_col].fillna(res_df[pred_col]).astype(np.float32)

    # Calculate error only where original data existed to compare against
    mask_orig = res_df[target_col].notna() & res_df[pred_col].notna()
    err_vals = (res_df.loc[mask_orig, target_col] - res_df.loc[mask_orig, pred_col]).abs().astype(np.float32)
    res_df[err_col] = np.nan
    res_df.loc[mask_orig, err_col] = err_vals
    res_df[err_col] = res_df[err_col].astype(np.float32)

    print(f"✅ {model_config['name']} imputation complete.")

    # ------------------------------------------------------------------
    # Derive summary metrics for backward compatibility with utilities
    # ------------------------------------------------------------------
    summary_mae = imputation_metrics.get('mae', -1)
    summary_r2 = imputation_metrics.get('r2', -1)
    summary_rmse = imputation_metrics.get('rmse', -1)

    # If imputation metrics are unavailable (=-1) fall back to prediction
    if summary_mae == -1:
        summary_mae = prediction_metrics.get('mae', np.nan)
    if summary_r2 == -1:
        summary_r2 = prediction_metrics.get('r2', np.nan)
    if summary_rmse == -1:
        summary_rmse = prediction_metrics.get('rmse', np.nan)

    # Compute a composite score similar to evaluate_model_comprehensive()
    if not np.isnan(summary_r2):
        r2_penalty = (1 - summary_r2) if summary_r2 > 0 else (1 + abs(summary_r2))
    else:
        r2_penalty = 1
    composite_score = (summary_mae * 0.5) + (summary_rmse * 0.3) + (r2_penalty * 0.2)

    eval_results = {
        'mae': summary_mae,
        'r2': summary_r2,
        'rmse': summary_rmse,
        'model_name': model_config['name'],
        'composite_score': composite_score,
        # Add comprehensive evaluation results
        'imputation_metrics': imputation_metrics,
        'prediction_metrics': prediction_metrics,
        'comprehensive_evaluation': comprehensive_evaluation
    }

    return res_df, {
        'target': target_col, 'evaluations': [eval_results],
        'best_model_name': model_config['name'], 'trained_models': {model_config['name']: model},
        # Add comprehensive evaluation to results
        'comprehensive_evaluation': comprehensive_evaluation,
        'imputation_metrics': imputation_metrics,
        'prediction_metrics': prediction_metrics
    }


# Export list for backward compatibility with preprocessing modules
__all__ = [
    # Core ML functions
    'impute_logs_deep',
    'MODEL_REGISTRY',
    'ensure_tensor_dtype_consistency',

    # Re-exported data handling functions for backward compatibility
    'normalize_data',
    'create_sequences',
    'introduce_missingness',
    'load_las_files_from_directory',
    'clean_log_data',
    'write_results_to_las'
]
