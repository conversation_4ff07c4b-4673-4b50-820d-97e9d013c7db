--- Running Model 1/1: saits ---
🚀 [MAXIMUM PERFORMANCE] Starting optimized training for saits
   Expected speedup: 4-6x
   Optimization level: aggressive
   GPU strategy: modern_gpu
[MEM] Applying memory optimization with Maximum Performance
[MEM] Memory cleared
🎯 GPU Hardware: NVIDIA T550 Laptop GPU (Compute 7.5)
   ⚡ Mixed precision enabled for modern GPU
🚀 Attempting optimized pipeline (level: aggressive)
🚀 Starting Optimized Phase 1 Training (Level: aggressive)...
   Model: SAITS (Self-Attention)
   Target: P-WAVE
   Features: ['GR', 'NPHI', 'RHOB', 'RT']
   📊 Dataset size: 41,898 rows, 6 wells (avg: 6983 rows/well)
   🔍 Pre-checking data sufficiency for aggressive optimization...
   📊 Wells analysis: 6/6 wells likely to pass (threshold: 11)
\n📊 Step 1: Optimized Data Preparation...
   Performing quick data quality assessment...
   📊 Small dataset detected (5,000 elements) - using adaptive quality threshold
   📈 Data quality score: 0.614 (finite rate: 59.4%)
   📊 Standard preprocessing required (score: 0.614 < 0.800)
Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=37066/41898 (88.5%)
Normalized 'NPHI': method=standard, valid_data=32087/41898 (76.6%)
Normalized 'RHOB': method=standard, valid_data=27539/41898 (65.7%)
Normalized 'RT': method=standard, valid_data=34120/41898 (81.4%)
Normalized 'P-WAVE': method=standard, valid_data=28027/41898 (66.9%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-15: 100%|███████████████████| 6/6 wells [00:07<00:00,  1.22s/well]

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 16
   • Total sequences created: 24,918
Enhanced sequences shape: (24918, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64
   Created 24918 sequences in 7.44s
\n🔍 Step 2: Vectorized Preprocessing Pipeline...
   🚀 Vectorized preprocessing: (24918, 64, 5)
   Vectorized preprocessing completed in 0.41s
\n🔧 Step 3: Smart Validation...
   Validation completed in 0.02s
   Sequences valid: ✅ STABLE
\n🎯 Step 4: Direct Tensor Training...
   📚 IMPUTATION MODE: Creating training sequences with missing values
Using enhanced missing value introduction with realistic patterns...
Introduced 26.1% missing values (2079385 elements)
Pattern: 717638 random + 1361747 chunked
Enhanced missing sequences shape: (24918, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64
   Tensor preparation completed in 11.55s
   Training tensor shape: torch.Size([24918, 64, 5])
   Truth tensor shape: torch.Size([24918, 64, 5])
   Efficient synthetic DataFrame created: (100, 7)
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['WELL_8', 'WELL_1', 'WELL_5', 'WELL_0', 'WELL_7', 'WELL_2', 'WELL_9', 'WELL_4'] 
  - Test wells: ['WELL_3', 'WELL_6']
   Using adaptive validation ratio: 15.0% (median well size: 10)
   🚨 Very small wells detected (min: 10) - reduced validation ratio to 10.0%
   Minimum well size threshold: 6 (validation ratio: 10.0%)
Flexible Split Report:
  - Wells for Training/Validation: 8
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 72
  - Validation Samples (Deeper part of train wells): 8
  - Test Samples (Entirely separate wells): 20

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TRAIN split:
     - NPHI: correlation = 0.951
     - RHOB: correlation = 0.993
     - RT: correlation = 1.000
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
🚨 TARGET LEAKAGE DETECTED!
   Suspicious correlations:
     - RHOB: 0.9912
     - RT: 0.9996
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 2/3
   Data quality score: 0.33

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   🚨 Target leakage detected in features!
   • Remove features that are identical to target
   • Check feature engineering pipeline for target contamination
   • Verify that future target values are not used as features
   • Review data preprocessing steps
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=72/72 (100.0%)
Normalized 'NPHI': method=standard, valid_data=72/72 (100.0%)
Normalized 'RHOB': method=standard, valid_data=72/72 (100.0%)
Normalized 'RT': method=standard, valid_data=72/72 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=72/72 (100.0%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|███████████████████| 8/8 wells [00:00<00:00, 65.26well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 64, 5), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 64, 5) (expected: (n_sequences, 64, 5))
   Data quality check:
     - Total wells: 8
     - Total rows: 72
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'P-WAVE']
     - Sequence length: 64, step: 1
Creating basic sequences | Current: WELL_9: 100%|█████████████| 8/8 wells [00:00<00:00, 51.24well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
⚠️ No sequences created with default length. Trying smaller sequence lengths...
   Trying sequence length: 32
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|███████████████████| 8/8 wells [00:00<00:00, 62.40well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 32, 5), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 32, 5) (expected: (n_sequences, 32, 5))
   Data quality check:
     - Total wells: 8
     - Total rows: 72
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'P-WAVE']
     - Sequence length: 32, step: 1
Creating basic sequences | Current: WELL_9: 100%|█████████████| 8/8 wells [00:00<00:00, 49.42well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
   Trying sequence length: 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 164.82well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 16, 5), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 16, 5) (expected: (n_sequences, 16, 5))
   Data quality check:
     - Total wells: 8
     - Total rows: 72
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'P-WAVE']
     - Sequence length: 16, step: 1
Creating basic sequences | Current: WELL_9: 100%|█████████████| 8/8 wells [00:00<00:00, 39.29well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
   Trying sequence length: 8
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|███████████████████| 8/8 wells [00:00<00:00, 22.30well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 16
Enhanced sequences shape: (16, 8, 5), type: <class 'numpy.ndarray'>, dtype: float64
Successfully created 16 sequences with length 8
Using enhanced normalization with winsorization...
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|███████████████████| 8/8 wells [00:00<00:00, 47.16well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 8, 5), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 8, 5) (expected: (n_sequences, 8, 5))
   Data quality check:
     - Total wells: 8
     - Total rows: 8
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'P-WAVE']
     - Sequence length: 8, step: 1
Creating basic sequences | Current: WELL_9: 100%|█████████████| 8/8 wells [00:00<00:00, 35.07well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 15.9% missing values (102 elements)
Pattern: 57 random + 45 chunked
Enhanced missing sequences shape: (16, 8, 5), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
❌ Optimized training failed: not enough values to unpack (expected 3, got 1)
   Falling back to original function...
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['B-G-6', 'B-G-10', 'B-L-15', 'B-L-2.G1']
  - Test wells: ['B-L-9', 'B-L-6']
   Using adaptive validation ratio: 30.0% (median well size: 8300)
   Minimum well size threshold: 12 (validation ratio: 30.0%)
Flexible Split Report:
  - Wells for Training/Validation: 4
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 21174
  - Validation Samples (Deeper part of train wells): 9078
  - Test Samples (Entirely separate wells): 11646