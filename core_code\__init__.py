"""Core code package for ML Log Prediction.

This package contains the core modules for machine learning-based well log prediction:
- data_handler: Data loading, cleaning, and preprocessing utilities
- ml_core: Machine learning models and training pipeline
- data_leakage_detector: Data leakage detection and validation utilities
"""

# Import key functions and classes for easy access
from .data_handler import (
    load_las_files_from_directory,
    clean_log_data,
    normalize_data,
    create_sequences,
    write_results_to_las
)

from .ml_core import (
    MODEL_REGISTRY,
    ensure_tensor_dtype_consistency
)

from .data_leakage_detector import (
    detect_perfect_correlation_leakage,
    validate_temporal_split,
    comprehensive_leakage_check
)

__version__ = "1.0.0"
__all__ = [
    # Data handler exports
    "load_las_files_from_directory",
    "clean_log_data", 
    "normalize_data",
    "create_sequences",
    "write_results_to_las",
    # ML core exports
    "MODEL_REGISTRY",
    "ensure_tensor_dtype_consistency",
    # Data leakage detector exports
    "detect_perfect_correlation_leakage",
    "validate_temporal_split",
    "comprehensive_leakage_check"
]