==================================================
🔍 ENVIRONMENT DIAGNOSTICS
==================================================
✅ MSVC: MSVC compiler available
❌ Windows SDK: Not detected
   💡 Install Windows SDK for development headers
✅ CUDA: CUDA toolkit detected: Cuda compilation tools, release 11.8, V11.8.89

📋 Summary:
   ✅ Some development tools available
==================================================
🔇 Compilation warnings suppressed for cleaner output

==================================================
🔍 ENVIRONMENT DIAGNOSTICS
==================================================
✅ MSVC: MSVC compiler available
❌ Windows SDK: Not detected
   💡 Install Windows SDK for development headers
✅ CUDA: CUDA toolkit detected: Cuda compilation tools, release 11.8, V11.8.89

📋 Summary:
   ✅ Some development tools available
==================================================
[PHASE1] Configuring memory optimization environment...
   [OK] PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\preprocessing\deep_model\phase1_preprocessing.py:56: UserWarning: Original ml_core functions not available. Some fallback functionality may be limited.
  warnings.warn("Original ml_core functions not available. Some fallback functionality may be limited.")
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\preprocessing\deep_model\phase1_integration.py:59: UserWarning: Original ml_core functions not available. Some fallback functionality may be limited.
  warnings.warn("Original ml_core functions not available. Some fallback functionality may be limited.")
Multiple Linear Regression utilities loaded successfully
Data leakage detection module loaded
Loading advanced deep learning models...
🔧 TensorFlow environment configured for compatibility
⚠️ TensorFlow DLL loading failed - this is a common Windows issue
💡 Suggestions:
   1. Install Microsoft Visual C++ Redistributable
   2. Use tensorflow-cpu instead of tensorflow
   3. Update to compatible numpy version
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\utils\tensorflow_compatibility.py:196: UserWarning: TensorFlow initialization failed: Traceback (most recent call last):
  File "C:\Users\<USER>\mwlt\lib\site-packages\tensorflow\python\pywrap_tensorflow.py", line 73, in <module>
    from tensorflow.python._pywrap_tensorflow_internal import *
ImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.


Failed to load the native TensorFlow runtime.
See https://www.tensorflow.org/install/errors for some common causes and solutions.
If you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.
  warnings.warn(f"TensorFlow initialization failed: {tf_error}")
🔧 TensorFlow environment configured for compatibility
⚠️ TensorFlow DLL loading failed - this is a common Windows issue
💡 Suggestions:
   1. Install Microsoft Visual C++ Redistributable
   2. Use tensorflow-cpu instead of tensorflow
   3. Update to compatible numpy version
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\models\advanced_models\saits_model.py:30: UserWarning: PyPOTS not available: PyPOTS requires TensorFlow: Traceback (most recent call last):
  File "C:\Users\<USER>\mwlt\lib\site-packages\tensorflow\python\pywrap_tensorflow.py", line 73, in <module>
    from tensorflow.python._pywrap_tensorflow_internal import *
ImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.


Failed to load the native TensorFlow runtime.
See https://www.tensorflow.org/install/errors for some common causes and solutions.
If you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.
  warnings.warn(f"PyPOTS not available: {pypots_error}")
SAITS model loaded successfully
🔧 TensorFlow environment configured for compatibility
⚠️ TensorFlow DLL loading failed - this is a common Windows issue
💡 Suggestions:
   1. Install Microsoft Visual C++ Redistributable
   2. Use tensorflow-cpu instead of tensorflow
   3. Update to compatible numpy version
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\models\advanced_models\brits_model.py:30: UserWarning: PyPOTS not available: PyPOTS requires TensorFlow: Traceback (most recent call last):
  File "C:\Users\<USER>\mwlt\lib\site-packages\tensorflow\python\pywrap_tensorflow.py", line 73, in <module>
    from tensorflow.python._pywrap_tensorflow_internal import *
ImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.


Failed to load the native TensorFlow runtime.
See https://www.tensorflow.org/install/errors for some common causes and solutions.
If you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.
  warnings.warn(f"PyPOTS not available: {pypots_error}")
BRITS model loaded successfully
Advanced models loaded: ['saits', 'brits']
Total available: 2/5 models
Advanced models module initialized (Phase 1 foundation)
Ready for Phase 2: Core model implementations
Advanced deep learning models module loaded
Available advanced models: ['saits', 'brits']
SAITS model added to registry
BRITS model added to registry
Enhanced MODEL_REGISTRY with 2 advanced models
Available advanced models: ['saits', 'brits']
   [OK] Phase 1 Enhanced Deep Learning Integration loaded
   [OK] Optimized pipeline functions available
INFO:utils.display_utils:Configuring fonts for Windows system
INFO:utils.display_utils:Set matplotlib font to: Segoe UI Emoji
INFO:utils.display_utils:Emoji support confirmed
INFO:utils.display_utils:Configured warning filters for font issues
⚠️ CUDA not available, all operations will use CPU
🔍 Performance Monitor initialized
   • Monitoring interval: 1.0s
[MEM] Environment configured for memory optimization
[MEM] Memory Optimizer initialized
   * Mixed precision enabled
   * Memory monitoring enabled
   [OK] Memory optimizer initialized
============================================================
 ML LOG PREDICTION
============================================================

[MEM] Initial Memory Status:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 14.2 GB
   * Usage: 55.2%
==================================================

Step 1: Select LAS files
Select LAS files using the file dialog...
No files selected.
No files selected. Exiting.
File selection cancelled. Exiting.